package com.vbs.capsAllocation.controller;

import com.vbs.capsAllocation.dto.AttendanceRequest;
import com.vbs.capsAllocation.dto.BaseResponse;
import com.vbs.capsAllocation.dto.CheckInStatusResponse;
import com.vbs.capsAllocation.model.Attendance;
import com.vbs.capsAllocation.model.ShiftDetails;
import com.vbs.capsAllocation.repository.AttendanceRepository;
import com.vbs.capsAllocation.repository.ShiftDetailsRepository;
import com.vbs.capsAllocation.service.AttendanceService;
import com.vbs.capsAllocation.service.EmployeeService;
import com.vbs.capsAllocation.util.LoggerUtil;
import com.vbs.capsAllocation.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/atom")
public class AttendanceController {

    @Autowired
    private ShiftDetailsRepository shiftDetailsRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AttendanceService attendanceService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private AttendanceRepository attendanceRepository;

    private static final Logger logger = LoggerFactory.getLogger(AttendanceController.class);

    @GetMapping("/currentUserLdap")
    public ResponseEntity<String> getCurrentUserLdap() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null ||
                !authentication.isAuthenticated() ||
                authentication.getPrincipal() instanceof String) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body("User not properly authenticated");
        }

        // For JWT authentication, the name should be the LDAP
        String ldap = authentication.getName();
        return ResponseEntity.ok("\"" + ldap + "\""); // Return as JSON string

    }

    @PostMapping("/mark")
    public ResponseEntity<Attendance> markAttendance(@RequestBody AttendanceRequest request) {
        try {
            Attendance result = attendanceService.markAttendance(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    @GetMapping("/checkingIn/{ldap}")
    @PreAuthorize("#ldap == authentication.name")
    public CheckInStatusResponse getCheckInStatus(@PathVariable String ldap, Authentication authentication) {
        // Security check: Ensure the authenticated user can only check their own status
        String authenticatedLdap = authentication.getName();
        
        logger.debug("Principal LDAP: {}, Path LDAP: {}", authenticatedLdap, ldap);
        
        if (!authenticatedLdap.equals(ldap)) {
            logger.warn("Security violation: User {} attempted to check status for user {}", authenticatedLdap, ldap);
            throw new RuntimeException("Access denied: You can only check your own attendance status");
        }
        
        return attendanceService.getCheckInStatus(ldap);
    }

    @PreAuthorize("hasRole('USER')")
    @GetMapping("/checkingIn")
    public CheckInStatusResponse getMyCheckInStatus(@AuthenticationPrincipal UserDetails user) {
        String ldap = user.getUsername();
        System.out.println("USER " + user);
        logger.debug("Getting check-in status for authenticated user: {}", ldap);
        return attendanceService.getCheckInStatus(ldap);
    }

    @GetMapping("/records/{ldap}")
    @PreAuthorize("#ldap == authentication.name")
    public ResponseEntity<List<Attendance>> getAttendanceByLdap(@PathVariable String ldap) {
        List<Attendance> records = attendanceRepository.findByEmployeeLdapOrderByEntryDateDesc(ldap);
        return ResponseEntity.ok(records);
    }
    
    @GetMapping("/records")
    public ResponseEntity<List<Attendance>> getMyAttendanceRecords(@AuthenticationPrincipal UserDetails user) {
        String ldap = user.getUsername();
        logger.debug("Getting attendance records for authenticated user: {}", ldap);
        List<Attendance> records = attendanceRepository.findByEmployeeLdapOrderByEntryDateDesc(ldap);
        return ResponseEntity.ok(records);
    }

    @PreAuthorize("hasRole('USER') or hasRole('LEAD') or hasRole('MANAGER') or hasRole('ACCOUNT_MANAGER') or hasRole('ADMIN_OPS_MANAGER')")
    @GetMapping
    public ResponseEntity<BaseResponse<List<Attendance>>> getMyAttendance(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            String ldap = userDetails.getUsername(); // Extract LDAP from authenticated user
            LoggerUtil.logDebug("Fetching attendance for user: {} from {} to {}", ldap, startDate, endDate);

            List<Attendance> records = attendanceService.getAttendanceRecords(ldap, startDate, endDate);
            return ResponseEntity.ok(
                    BaseResponse.success("Attendance records retrieved successfully", records)
            );
        } catch (Exception e) {
            LoggerUtil.logError("Error fetching attendance records: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error(
                            "Failed to retrieve attendance records: " + e.getMessage(),
                            HttpStatus.INTERNAL_SERVER_ERROR.value()
                    ));
        }
    }

    // ShiftController.java
    @GetMapping("/shift-details/{code}")
    public ResponseEntity<ShiftDetails> getShiftDetailsByCode(@PathVariable String code) {
        ShiftDetails shift = shiftDetailsRepository.findByCode(code)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Shift not found"));
        return ResponseEntity.ok(shift);
    }

}

