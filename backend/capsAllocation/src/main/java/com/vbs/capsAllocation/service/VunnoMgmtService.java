package com.vbs.capsAllocation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vbs.capsAllocation.dto.BaseResponse;
import com.vbs.capsAllocation.dto.LeaveBalanceUploadDto;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.vbs.capsAllocation.dto.VunnoMgmtDto;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import com.vbs.capsAllocation.model.*;
import com.vbs.capsAllocation.repository.*;
import com.vbs.capsAllocation.util.EmailTemplateUtil;
import com.vbs.capsAllocation.util.LoggerUtil;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.web.multipart.MultipartFile;

@Service
public class VunnoMgmtService {

    @Autowired
    private static final String APPLICATION_NAME = "Google Sheets API Java Quickstart";
    private static final String TOKENS_DIRECTORY_PATH = "tokens";

    @Value("${google.sheets.spreadsheetId}")
    private String spreadsheetId;

    @Value("${google.sheets.sheetName}")
    private String sheetName;

    @Autowired
    private VunnoAuditRepository vunnoAuditRepository;

    @Autowired
    private VunnoResponseRepository vunnoResponseRepository;

    @Autowired
    private LeaveBalanceRepository leaveBalanceRepository;

    @Autowired
    private LeaveUsageLogRepository leaveUsageLogRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private ApprovalTokenService approvalTokenService;

    @Autowired
    private EmailTemplateUtil emailTemplateUtil;

    @Autowired
    private EmployeeRepository employeeRepository;

    private static final Logger logger = LoggerFactory.getLogger(VunnoMgmtService.class);

    public List<Map<String, String>> getFilteredSheetData(String ldap) {
        List<VunnoResponse> responses = vunnoResponseRepository.findByEmployeeLdap(ldap);

        if (responses.isEmpty()) {
            logger.info("No records found in database for LDAP: {}", ldap);
            return Collections.emptyList();
        }

        List<Map<String, String>> filteredData = new ArrayList<>();

        for (VunnoResponse response : responses) {
            Map<String, String> rowData = new HashMap<>();
            rowData.put("Status", response.getStatus());
            rowData.put("Requestor Name",response.getRequestorName());
            rowData.put("Request Type", response.getApplicationType());
            rowData.put("Leave Type", response.getLeaveType());
            rowData.put("Start Date", response.getFromDate() != null ? response.getFromDate().toString() : "");
            rowData.put("End Date", response.getToDate() != null ? response.getToDate().toString() : "");
            rowData.put("Duration", response.getDuration() != null ? response.getDuration() : "");
            rowData.put("Approver", response.getApprover());

            filteredData.add(rowData);
        }

        logger.info("Returning {} records for LDAP: {}",  ldap);
        return filteredData;
    }

    public String uploadLeaveBalances(MultipartFile file, UserDetails userDetails) {
        try {
            String uploader = userDetails.getUsername();
            Integer year = (Integer) Year.now().getValue();  // changed to Integer
            Integer month = (Integer) LocalDate.now().getMonthValue();  // changed to Integer

            List<LeaveBalanceUploadDto> rows = parseLeaveBalanceSheet(file);

            for (LeaveBalanceUploadDto row : rows) {
                Optional<Employee> empOpt = employeeRepository.findByLdap(row.getLdap().trim());
                if (empOpt.isEmpty()) continue;

                Employee employee = empOpt.get();

                saveOrUpdateBalance(employee, "SL", row.getSlBalance(), year, month, uploader);
                saveOrUpdateBalance(employee, "CL", row.getClBalance(), year, month, uploader);
                saveOrUpdateBalance(employee, "EL", row.getElBalance(), year, month, uploader);

                // Always set WFH = 0 if not already present
                Optional<LeaveBalance> wfhOpt = leaveBalanceRepository
                        .findByEmployeeAndLeaveTypeAndMonthAndYear(employee, "WFH", month, year);
                if (wfhOpt.isEmpty()) {
                    saveOrUpdateBalance(employee, "WFH", Double.valueOf(0.0), year, month, uploader); // fixed type
                }
            }

            return "Leave balances uploaded successfully.";
        } catch (Exception e) {
            e.printStackTrace();
            return "Upload failed: " + e.getMessage();
        }
    }


    private List<LeaveBalanceUploadDto> parseLeaveBalanceSheet(MultipartFile file) throws IOException {
        List<LeaveBalanceUploadDto> rows = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0); // assumes first sheet is the active one
            for (int i = 1; i <= sheet.getLastRowNum(); i++) { // skip header row
                Row row = sheet.getRow(i);
                if (row == null) continue;

                String ldap = getCellString(row.getCell(0));
                double sl = getCellDouble(row.getCell(3));
                double cl = getCellDouble(row.getCell(4));
                double el = getCellDouble(row.getCell(5));

                if (ldap == null || ldap.isBlank()) continue;

                rows.add(new LeaveBalanceUploadDto(ldap.trim(), sl, cl, el));
            }
        }

        return rows;
    }

    private String getCellString(Cell cell) {
        return cell != null ? cell.toString().trim() : null;
    }

    private double getCellDouble(Cell cell) {
        if (cell == null) return 0.0;
        if (cell.getCellType() == CellType.NUMERIC) return cell.getNumericCellValue();
        try {
            return Double.parseDouble(cell.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }


    private void saveOrUpdateBalance(Employee employee, String type, Double balance, Integer year, Integer month, String uploadedBy) {
        Optional<LeaveBalance> opt = leaveBalanceRepository
                .findByEmployeeAndLeaveTypeAndMonthAndYear(employee, type, month, year);

        LeaveBalance lb = opt.orElseGet(() -> new LeaveBalance(employee, year, month, type, Double.valueOf(0.0))); // fixed type
        lb.setBalance(balance);
        lb.setSource("UPLOAD");
        lb.setUploadedBy(uploadedBy);
        lb.setUploadedAt(LocalDateTime.now());

        leaveBalanceRepository.save(lb);
    }



    public String setRequestedVunno(VunnoRequestDto requestDto) {
        logger.info("setRequestedVunno function of VunnoMgmtService Class Started");

        String returnValue = "";

        if(requestDto.getStatus().equalsIgnoreCase("PENDING")){
            logger.info("Processing request for approval");
            returnValue = requestingLeave(requestDto);
        }
        else {
            return "Error in processing your request";
        }

        logger.info("setRequestedVunno function of VunnoMgmtService Class Ended");
        return returnValue;
    }

    // Helper method
    private String getQuarter(LocalDate date) {
        int month = date.getMonthValue();
        if(month <= 3) return "Q1";
        else if(month <= 6) return "Q2";
        else if(month <= 9) return "Q3";
        else return "Q4";
    }




    // Helper method to get quarter from month
    private String getQuarterFromMonth(int month) {
        if (month >= 1 && month <= 3) {
            return "Q1";
        } else if (month >= 4 && month <= 6) {
            return "Q2";
        } else if (month >= 7 && month <= 9) {
            return "Q3";
        } else {
            return "Q4";
        }
    }

    // Helper method to safely get String values from a row
    private String getStringValue(List<Object> row, List<Object> headers, String columnName) {
        int index = headers.indexOf(columnName);
        return (index != -1 && index < row.size()) ? row.get(index).toString() : null;
    }

    public String requestingLeave(VunnoRequestDto requestDto) {
        try {
            logger.info("Fetching Details of lead-Manager");
            List<VunnoMgmtDto> MgmtDetails = getLeadManagerDetails(requestDto.getLdap());

            if (MgmtDetails == null || MgmtDetails.isEmpty()) {
                throw new RuntimeException("Lead manager details not found for LDAP: " + requestDto.getLdap());
            }

            VunnoMgmtDto dto = MgmtDetails.get(0);
            String program = dto.getProgramAlignment();
            String team = dto.getTeam();
            String name = dto.getName();

            final String DOMAIN = "@google.com";
            String approvingLead = requestDto.getApprovingLead();
            String requester = requestDto.getLdap() + DOMAIN;
            String ldap = requestDto.getLdap();
            String applicationType = requestDto.getApplicationType();
            String leaveType = "Leave".equalsIgnoreCase(applicationType) ? requestDto.getLeaveType() : "";
            String lvwfhDuration = requestDto.getLvWfhDuration();
            String backupInfo = requestDto.getBackupInfo();
            String oooProof = requestDto.getOooProof();
            String timesheetProof = requestDto.getTimesheetProof();

            // Format dates
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate fromDate = LocalDate.parse(requestDto.getStartDate(), dateFormatter);
            LocalDate toDate = LocalDate.parse(requestDto.getEndDate(), dateFormatter);

            // Fetch Employee
            Employee employee = employeeRepository.findByLdap(ldap)
                    .orElseThrow(() -> new RuntimeException("Employee not found for LDAP: " + ldap));

            // Create VunnoResponse entity
            VunnoResponse response = new VunnoResponse();
            response.setTimestamp(LocalDateTime.now());
            response.setTeam(team);
            response.setRequestorName(name);
            response.setApprover(approvingLead);
            response.setFromDate(fromDate);
            response.setToDate(toDate);
            response.setApplicationType(applicationType);
            response.setLeaveType(leaveType);
            response.setDuration(lvwfhDuration);
            response.setProgram(program);
            response.setStatus(requestDto.getStatus()); // Should be "PENDING"
            response.setBackup(backupInfo);
            response.setOrgScreenshot(oooProof);
            response.setTimesheetScreenshot(timesheetProof);
            response.setEmployee(employee);

            // Save to database
            vunnoResponseRepository.save(response);

            logger.info("Leave request saved successfully for " + ldap);
            return "Requested leave successfully saved into database";

        } catch (Exception e) {
            logger.error("An error occurred in requestingLeave method", e);
            return "An error occurred while processing the request: " + e.getMessage();
        }
    }

    // Fetch Leads Information when passed Ldap
    public List<VunnoMgmtDto> getLeadManagerDetails(String ldap) {
        if (ldap == null || ldap.isEmpty()) {
            throw new IllegalArgumentException("LDAP Cannot be Empty");
        }

        Employee employee = employeeRepository.findByLdap(ldap)
                .orElseThrow(() -> new RuntimeException("Employee not found"));

        String fullName = employee.getFirstName() + " " + employee.getLastName();

        return Collections.singletonList(new VunnoMgmtDto(
                employee.getLdap(),
                fullName,
                employee.getLevel(),
                employee.getEmail(),
                employee.getPnseProgram(),
                employee.getTeam(),
                employee.getLead(),
                employee.getProgramManager(),
                employee.getShift()
        ));
    }

    public List<Double> getAllCountOfLeaveWFH(String ldap) {
        // Fetch leave balances
        List<LeaveBalance> balances = leaveBalanceRepository.findByEmployeeLdap(ldap);
        double sl = 0, cl = 0, el = 0;
        for (LeaveBalance balance : balances) {
            switch (balance.getLeaveType()) {
                case "SL": sl = balance.getBalance(); break;
                case "CL": cl = balance.getBalance(); break;
                case "EL": el = balance.getBalance(); break;
            }
        }

        // Fetch WFH usage
        double totalWFH = leaveUsageLogRepository.sumDaysTakenByLdapAndLeaveType(ldap, "WFH");
        double quarterWFH = leaveUsageLogRepository.sumDaysTakenByLdapAndLeaveTypeAndQuarter(
                ldap, "WFH", getCurrentQuarter(), String.valueOf(Year.now().getValue())
        );

        return List.of(sl, cl, el, sl + cl + el, totalWFH, quarterWFH);
    }

    // Helper method to get current quarter as a string (Q1, Q2, Q3, or Q4)
    public String getCurrentQuarter() {
        LocalDate date = LocalDate.now();
        int month = date.getMonthValue();
        if (month >= 1 && month <= 3) {
            return "Q1";
        } else if (month >= 4 && month <= 6) {
            return "Q2";
        } else if (month >= 7 && month <= 9) {
            return "Q3";
        } else {
            return "Q4";
        }

    }

    public String deleteLeaveRequestWithPermission(Long id, UserDetails userDetails) {
        String loggedInUser = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        Optional<VunnoResponse> optional = vunnoResponseRepository.findById(id);
        if (optional.isEmpty()) {
            throw new IllegalArgumentException("Leave request not found.");
        }

        VunnoResponse request = optional.get();

        request.setStatus("DELETED");
        request.setDeletedAt(LocalDateTime.now());
        vunnoResponseRepository.save(request);

        boolean auditLogged = logDeleteAction(request, loggedInUser, roles);
        if (!auditLogged) {
            throw new IllegalStateException("Failed to log audit for deletion.");
        }
        return "Leave Request Successfully Deleted!";
    }

    public boolean logDeleteAction(VunnoResponse request, String loggedInUser, List<String> roles) {
        try {
            System.out.println("ROLES DELETE " + roles);
            VunnoAuditLog auditLog = new VunnoAuditLog();
            auditLog.setVunnoResponseId(request.getId());
            auditLog.setActionType("DELETE");
            auditLog.setPreviousStatus("PENDING");
            auditLog.setNewStatus("DELETED");
            auditLog.setChangedBy(loggedInUser);
            auditLog.setChangedByRole(String.join(",", roles));
            auditLog.setChangedAt(LocalDateTime.now());
            auditLog.setChangeDescription("Deleted by LoggedIn User");

            vunnoAuditRepository.save(auditLog);
            return true;
        } catch (Exception e) {
            LoggerUtil.logError("Error logging audit record: {}", e.getMessage(), e);
            return false;
        }
    }

    public String updateLeaveRequestWithPermission(Long id, VunnoRequestDto updateRequest, UserDetails userDetails) {
        String loggedInUser = userDetails.getUsername();

        Optional<VunnoResponse> optional = vunnoResponseRepository.findById(id);
        if (optional.isEmpty()) {
            throw new IllegalArgumentException("Leave request not found.");
        }

        System.out.println("UpdatedRequest " + updateRequest);

        VunnoResponse request = optional.get();

        // Update fields based on VunnoRequestDto
        request.setFromDate(OffsetDateTime.parse(updateRequest.getStartDate()).toLocalDate());
        request.setToDate(OffsetDateTime.parse(updateRequest.getEndDate()).toLocalDate());

        // Application type and duration
        request.setApplicationType(updateRequest.getApplicationType());
        request.setDuration(updateRequest.getLvWfhDuration());

        // Leave type logic
        if ("Leave".equalsIgnoreCase(updateRequest.getApplicationType())) {
            request.setLeaveType(updateRequest.getLeaveType());
        } else {
            request.setLeaveType(null);
        }

        if (updateRequest.getStatus() != null) {
            request.setStatus(updateRequest.getStatus());
        }
        if (updateRequest.getApprovingLead() != null) {
            request.setApprover(updateRequest.getApprovingLead());
        }
        // Optionally update other fields (e.g., backup info, ooo proof, timesheet) if needed
        request.setBackup(updateRequest.getBackupInfo());

        vunnoResponseRepository.save(request);

        // Audit log
        boolean auditLogged = logUpdateAction(
                request,
                loggedInUser,
                userDetails.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()),
                "Leave request updated"
        );

        if (!auditLogged) {
            throw new IllegalStateException("Failed to log audit for update.");
        }

        return "Leave request updated successfully.";
    }

    public boolean logUpdateAction(
            VunnoResponse updatedRequest,
            String changedBy,
            List<String> roles,
            String reason
    )
    {
        try {
            // Fetch the previous state from DB before update
            Optional<VunnoResponse> existingOptional = vunnoResponseRepository.findById(updatedRequest.getId());
            if (existingOptional.isEmpty()) {
                return false;
            }

            VunnoResponse previous = existingOptional.get();

            // Prepare previous and new values as JSON-like maps
            Map<String, Object> previousValues = new HashMap<>();
            Map<String, Object> newValues = new HashMap<>();

            previousValues.put("fromDate", previous.getFromDate());
            previousValues.put("toDate", previous.getToDate());
            previousValues.put("applicationType", previous.getApplicationType());
            previousValues.put("duration", previous.getDuration());
            previousValues.put("leaveType", previous.getLeaveType());
            previousValues.put("backup", previous.getBackup());

            newValues.put("fromDate", updatedRequest.getFromDate());
            newValues.put("toDate", updatedRequest.getToDate());
            newValues.put("applicationType", updatedRequest.getApplicationType());
            newValues.put("duration", updatedRequest.getDuration());
            newValues.put("leaveType", updatedRequest.getLeaveType());
            newValues.put("backup", updatedRequest.getBackup());

            // Convert maps to JSON strings
            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());

            String previousJson = mapper.writeValueAsString(previousValues);
            String newJson = mapper.writeValueAsString(newValues);

            VunnoAuditLog auditLog = new VunnoAuditLog();
            auditLog.setVunnoResponseId(updatedRequest.getId());
            auditLog.setActionType("EDIT");
            auditLog.setPreviousStatus("PENDING");
            auditLog.setNewStatus(updatedRequest.getStatus());
            auditLog.setChangedBy(changedBy);
            auditLog.setChangedByRole(String.join(",", roles));
            auditLog.setChangedAt(LocalDateTime.now());
            auditLog.setChangeReason(reason);
            auditLog.setPreviousValues(previousJson);
            auditLog.setNewValues(newJson);


            vunnoAuditRepository.save(auditLog);
            return true;

        } catch (Exception e) {
            LoggerUtil.logError("Error logging update audit record: {}", e.getMessage(), e);
            return false;
        }
    }

    @Transactional(readOnly = true)
    public List<VunnoRequestDto> getApprovedRequests(UserDetails userDetails) {
        String ldap = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        List<String> targetLdaps = new ArrayList<>();

        if (roles.contains("ROLE_ADMIN_OPS_MANAGER")) {
            targetLdaps = employeeRepository.findAllLdaps();
        } else if (roles.contains("ROLE_MANAGER")) {
            targetLdaps = employeeRepository.findLdapsByManager(ldap);
        } else if (roles.contains("ROLE_LEAD")) {
            targetLdaps = employeeRepository.findLdapsByLead(ldap);
        }

        if (targetLdaps.isEmpty()) {
            return Collections.emptyList();
        }

        List<VunnoResponse> responses = vunnoResponseRepository.findApprovedRequestsByTeamLdaps(targetLdaps);

        return responses.stream().map(r -> {
            Employee emp = r.getEmployee();
            String requestorFullName = emp != null ? emp.getFirstName() + " " + emp.getLastName() : "";

            return new VunnoRequestDto(
                    r.getId(),
                    emp != null ? emp.getLdap() : null,
                    r.getApprover(),
                    r.getApplicationType(),
                    r.getLeaveType(),
                    r.getDuration(),
                    r.getFromDate() != null ? r.getFromDate().toString() : null,
                    r.getToDate() != null ? r.getToDate().toString() : null,
                    null,
                    null,
                    r.getBackup(),
                    r.getOrgScreenshot(),
                    r.getTimesheetScreenshot(),
                    r.getStatus(),
                    requestorFullName
            );
        }).collect(Collectors.toList());
    }


    @Transactional(readOnly = true)
    public List<VunnoRequestDto> getRequestsForApproval(UserDetails userDetails) {
        String ldap = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        List<String> targetLdaps = new ArrayList<>();

        if (roles.contains("ROLE_ADMIN_OPS_MANAGER")) {
            targetLdaps = employeeRepository.findAllLdaps();
        } else if (roles.contains("ROLE_MANAGER")) {
            targetLdaps = employeeRepository.findLdapsByManager(ldap);
        } else if (roles.contains("ROLE_LEAD")) {
            targetLdaps = employeeRepository.findLdapsByLead(ldap);
        }

        if (targetLdaps.isEmpty()) {
            return Collections.emptyList();
        }

        List<VunnoResponse> responses = vunnoResponseRepository.findPendingRequestsByTeamLdaps(targetLdaps);

        // Access within the transaction
        return responses.stream().map(r -> {
            Employee emp = r.getEmployee();
            String requestorFullName = emp != null ? emp.getFirstName() + " " + emp.getLastName() : "";

            return new VunnoRequestDto(
                    r.getId(),
                    emp != null ? emp.getLdap() : null,
                    r.getApprover(),
                    r.getApplicationType(),
                    r.getLeaveType(),
                    r.getDuration(),
                    r.getFromDate() != null ? r.getFromDate().toString() : null,
                    r.getToDate() != null ? r.getToDate().toString() : null,
                     null,
                    null,
                    r.getBackup(),
                    r.getOrgScreenshot(),
                    r.getTimesheetScreenshot(),
                    r.getStatus(),
                    requestorFullName // new field
            );
        }).collect(Collectors.toList());
    }

    private String getRoleFromUserDetails(UserDetails userDetails) {
        return userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .findFirst()
                .orElse("UNKNOWN");
    }

    @Transactional
    public String rejectRequest(VunnoRequestDto requestDto, UserDetails userDetails) {

        Long requestId = requestDto.getId();
        String approverLdap = userDetails.getUsername();
        String role = getRoleFromUserDetails(userDetails);

        VunnoResponse response = vunnoResponseRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Request not found with ID: " + requestId));

        String previousStatus = response.getStatus();
        if (!"PENDING".equalsIgnoreCase(previousStatus)) {
            return "Only PENDING requests can be rejected.";
        }

        response.setStatus("REJECTED");
        response.setApprover(approverLdap);
        vunnoResponseRepository.save(response);

        logAudit(response, "REJECT", "REJECTED", approverLdap, role, previousStatus, "Request rejected by " + role);

        return "Request rejected successfully.";
    }

    @Transactional
    public String revokeRequest(VunnoRequestDto requestDto, UserDetails userDetails) {
        Long requestId = requestDto.getId();
        String revokerLdap = userDetails.getUsername();
        String role = getRoleFromUserDetails(userDetails);

        VunnoResponse response = vunnoResponseRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Request not found with ID: " + requestId));

        String currentStatus = response.getStatus();
        if (!"APPROVED".equalsIgnoreCase(currentStatus)) {
            return "Only APPROVED requests can be revoked.";
        }

        LocalDate today = LocalDate.now();
        if (today.isAfter(response.getToDate())) {
            return "Request cannot be revoked after its end date.";
        }

        // Adjust leave balance
        revertLeaveOrWfh(response);

        response.setStatus("REVOKED");
        vunnoResponseRepository.save(response);

        logAudit(response, "REVOKE", "REVOKED", revokerLdap, role, currentStatus, "Request revoked by " + role);

        return "Request revoked successfully.";
    }

    @Transactional
    public String approveRequest(VunnoRequestDto requestDto, UserDetails userDetails) {

        Long requestId = requestDto.getId();
        String approverLdap = userDetails.getUsername();
        String role = getRoleFromUserDetails(userDetails);

        VunnoResponse response = vunnoResponseRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Request not found with ID: " + requestId));

        String previousStatus = response.getStatus();
        if (!"PENDING".equalsIgnoreCase(previousStatus)) {
            return "Only PENDING requests can be approved.";
        }

        response.setStatus("APPROVED");
        response.setApprover(approverLdap);
        vunnoResponseRepository.save(response);

        // Update leave usage or WFH
        updateUsageOrWfh(response);

        // Log audit
        logAudit(response, "APPROVE", "APPROVED",approverLdap, role, previousStatus, "Request approved by " + role);

        return "Request approved successfully.";
    }

    @Transactional(readOnly = true)
    public List<VunnoRequestDto> getProcessedRequestsForApproval(UserDetails userDetails, String statusFilter) {
        String ldap = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        List<String> targetLdaps = new ArrayList<>();

        if (roles.contains("ROLE_ADMIN_OPS_MANAGER")) {
            targetLdaps = employeeRepository.findAllLdaps();
        } else if (roles.contains("ROLE_MANAGER")) {
            targetLdaps = employeeRepository.findLdapsByManager(ldap);
        } else if (roles.contains("ROLE_LEAD")) {
            targetLdaps = employeeRepository.findLdapsByLead(ldap);
        }

        if (targetLdaps.isEmpty()) {
            return Collections.emptyList();
        }

        // Defaults to all processed statuses if status is not provided
        List<String> statuses = statusFilter != null
                ? List.of(statusFilter)
                : List.of("APPROVED", "REJECTED", "REVOKED");

        List<VunnoResponse> responses =
                vunnoResponseRepository.findByEmployeeLdapInAndStatusIn(targetLdaps, statuses);

        return responses.stream().map(r -> {
            Employee emp = r.getEmployee();
            String requestorFullName = emp != null ? emp.getFirstName() + " " + emp.getLastName() : "";

            return new VunnoRequestDto(
                    r.getId(),
                    emp != null ? emp.getLdap() : null,
                    r.getApprover(),
                    r.getApplicationType(),
                    r.getLeaveType(),
                    r.getDuration(),
                    r.getFromDate() != null ? r.getFromDate().toString() : null,
                    r.getToDate() != null ? r.getToDate().toString() : null,
                    null,
                    null,
                    r.getBackup(),
                    r.getOrgScreenshot(),
                    r.getTimesheetScreenshot(),
                    r.getStatus(),
                    requestorFullName
            );
        }).collect(Collectors.toList());
    }


    private void updateUsageOrWfh(VunnoResponse response) {
        Employee employee = response.getEmployee();
        String applicationType = response.getApplicationType(); // Leave or Work From Home
        LocalDate fromDate = response.getFromDate();
        LocalDate toDate = response.getToDate();
        int currentYear = fromDate.getYear();
        Month currentMonth = fromDate.getMonth();

        if ("Leave".equalsIgnoreCase(applicationType)) {
            double totalDaysTaken = calculateDaysTaken(response.getDuration(), fromDate, toDate);
            double remainingToDeduct = totalDaysTaken;

            // Determine primary leave type
            String primaryType = response.getLeaveType();
            List<String> deductionOrder;
            switch (primaryType) {
                case "CL":
                    deductionOrder = List.of("CL", "SL", "EL");
                    break;
                case "EL":
                    deductionOrder = List.of("EL", "CL", "SL");
                    break;
                default:
                    deductionOrder = List.of("SL", "CL", "EL");
                    break;
            }

            Map<String, LeaveBalance> balances = leaveBalanceRepository
                    .findByEmployeeAndMonthAndYear(employee, currentMonth.getValue(), currentYear)
                    .stream()
                    .collect(Collectors.toMap(LeaveBalance::getLeaveType, b -> b));

            List<LeaveUsageLog> logs = new ArrayList<>();

            for (String type : deductionOrder) {
                if (remainingToDeduct <= 0) break;

                LeaveBalance balance = balances.get(type);
                if (balance == null) continue;

                double available = balance.getBalance();
                double deducted = Math.min(available, remainingToDeduct);

                if (deducted > 0) {
                    balance.setBalance(Double.valueOf(available - deducted));
                    leaveBalanceRepository.save(balance);

                    LeaveUsageLog log = new LeaveUsageLog();
                    log.setEmployee(employee);
                    log.setLeaveType(type);
                    log.setDaysTaken(Double.valueOf(deducted));
                    log.setLeaveDate(fromDate);
                    log.setYear(Integer.valueOf(currentYear));
                    log.setQuarter(getQuarter(fromDate));
                    logs.add(log);

                    remainingToDeduct -= deducted;
                }
            }

            if (remainingToDeduct > 0) {
                throw new LeaveBalanceException("Insufficient leave balance across SL, CL, EL. Requested: " + totalDaysTaken);
            }

            leaveUsageLogRepository.saveAll(logs);

        }
        else if ("Work From Home".equalsIgnoreCase(applicationType)) {
            double daysTaken;

            switch (response.getDuration()) {
                case "Full Day" -> daysTaken = 1.0;
                case "Half Day AM", "Half Day PM" -> daysTaken = 0.5;
                case "Multiple Days" -> {
                    if (fromDate == null || toDate == null) {
                        throw new LeaveBalanceException("FromDate or ToDate missing for Multiple Days WFH.");
                    }
                    long days = ChronoUnit.DAYS.between(fromDate, toDate) + 1;
                    daysTaken = (double) days;
                }
                default -> throw new LeaveBalanceException("Invalid WFH duration: " + response.getDuration());
            }

            // Load WFH balance
            Optional<LeaveBalance> optionalWfh = leaveBalanceRepository
                    .findByEmployeeAndLeaveTypeAndMonthAndYear(employee, "WFH", currentMonth.getValue(), currentYear);

            if (optionalWfh.isEmpty()) {
                throw new LeaveBalanceException("WFH balance not initialized for " + currentMonth + " " + currentYear + ". Please upload before approving.");
            }

            LeaveBalance wfhBalance = optionalWfh.get();

            // Update balance
            wfhBalance.setBalance(wfhBalance.getBalance() + daysTaken);
            leaveBalanceRepository.save(wfhBalance);

            // Save WFH usage log
            LeaveUsageLog wfhLog = new LeaveUsageLog();
            wfhLog.setEmployee(employee);
            wfhLog.setLeaveType("WFH");
            wfhLog.setDaysTaken(daysTaken);
            wfhLog.setLeaveDate(fromDate);
            wfhLog.setYear(currentYear);
            wfhLog.setQuarter(getQuarter(fromDate));

            leaveUsageLogRepository.save(wfhLog);
        }

    }

    public class LeaveBalanceException extends RuntimeException {
        public LeaveBalanceException(String message) {
            super(message);
        }
    }


    private double calculateDaysTaken(String duration, LocalDate fromDate, LocalDate toDate) {
        if (duration == null || fromDate == null || toDate == null) {
            throw new LeaveBalanceException("Missing duration or dates for leave calculation.");
        }

        duration = duration.trim().toLowerCase();

        if (duration.contains("half")) {
            return 0.5;
        } else if (duration.contains("full")) {
            return 1.0;
        } else if (duration.contains("multiple")) {
            long days = ChronoUnit.DAYS.between(fromDate, toDate) + 1; // Inclusive
            return (double) days;
        } else {
            throw new LeaveBalanceException("Unknown duration format: " + duration);
        }
    }

    private void revertLeaveOrWfh(VunnoResponse response) {
        Employee employee = response.getEmployee();
        String applicationType = response.getApplicationType();
        LocalDate fromDate = response.getFromDate();
        LocalDate toDate = response.getToDate();
        int currentYear = fromDate.getYear();
        Month currentMonth = fromDate.getMonth();

        if ("Leave".equalsIgnoreCase(applicationType)) {
            String primaryType = response.getLeaveType();

            List<String> deductionOrder;
            switch (primaryType) {
                case "CL": deductionOrder = List.of("CL", "SL", "EL"); break;
                case "EL": deductionOrder = List.of("EL", "CL", "SL"); break;
                case "Long Leave": deductionOrder = List.of("EL", "CL", "SL"); break;
                default: deductionOrder = List.of("SL", "CL", "EL"); break;
            }

            List<LeaveUsageLog> logs = leaveUsageLogRepository
                    .findByEmployeeAndLeaveDateBetweenAndYear(employee, fromDate, toDate, currentYear)
                    .stream()
                    .filter(log -> deductionOrder.contains(log.getLeaveType()))
                    .sorted(Comparator.comparingInt(log -> deductionOrder.indexOf(log.getLeaveType())))
                    .collect(Collectors.toList());

            for (LeaveUsageLog log : logs) {
                LeaveBalance balance = leaveBalanceRepository
                        .findByEmployeeAndLeaveTypeAndMonthAndYear(employee, log.getLeaveType(), currentMonth.getValue(), currentYear)
                        .orElseThrow(() -> new RuntimeException("Leave balance not found for " + log.getLeaveType()));

                balance.setBalance(balance.getBalance() + log.getDaysTaken());
                leaveBalanceRepository.save(balance);
            }

            leaveUsageLogRepository.deleteAll(logs);

        }
        else if ("Work From Home".equalsIgnoreCase(applicationType)) {
            double daysToRevert;

            switch (response.getDuration()) {
                case "Full Day" -> daysToRevert = 1.0;
                case "Half Day AM", "Half Day PM" -> daysToRevert = 0.5;
                case "Multiple Days" -> {
                    if (fromDate == null || toDate == null) {
                        throw new LeaveBalanceException("FromDate or ToDate missing for Multiple Days WFH.");
                    }
                    long days = ChronoUnit.DAYS.between(fromDate, toDate) + 1;
                    daysToRevert = (double) days;
                }
                default -> throw new LeaveBalanceException("Invalid WFH duration: " + response.getDuration());
            }

            LeaveBalance wfhBalance = leaveBalanceRepository
                    .findByEmployeeAndLeaveTypeAndMonthAndYear(employee, "WFH", currentMonth.getValue(), currentYear)
                    .orElseThrow(() -> new RuntimeException("WFH balance not found"));

            // Subtract the previously added days
            wfhBalance.setBalance(wfhBalance.getBalance() - daysToRevert);
            leaveBalanceRepository.save(wfhBalance);

            // Delete related usage log(s)
            List<LeaveUsageLog> wfhLogs = leaveUsageLogRepository
                    .findByEmployeeAndLeaveTypeAndLeaveDateBetweenAndYear(
                            employee, "WFH", fromDate, toDate, currentYear
                    );

            leaveUsageLogRepository.deleteAll(wfhLogs);
        }

    }


    private void logAudit(VunnoResponse response, String actionType, String newStatus, String user, String role, String previousStatus, String description) {
        VunnoAuditLog log = new VunnoAuditLog();
        log.setVunnoResponseId(response.getId());
        log.setActionType(actionType);
        log.setPreviousStatus(previousStatus);
        log.setNewStatus(newStatus);
        log.setChangedBy(user);
        log.setChangedByRole(role);
        log.setChangedAt(LocalDateTime.now());
        log.setChangeReason(null); // If needed
        log.setChangeDescription(description);
        log.setPreviousValues(previousStatus);
        log.setNewValues(newStatus);

        vunnoAuditRepository.save(log);
    }
}