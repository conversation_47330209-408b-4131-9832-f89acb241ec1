package com.vbs.capsAllocation.service;

import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

@Service
public class EmailService {

    private final JavaMailSender mailSender;

    public EmailService(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }

    public void sendEmail(String to, String subject, String body, String cc) {
        try {
            System.out.println("Email started");
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            System.out.println("EMail");
            helper.setTo(to);

            // Only set CC if it's not null or empty
            if (cc != null && !cc.trim().isEmpty()) {
                helper.setCc(cc);
            }

            helper.setSubject(subject);
            helper.setText(body, true);
            System.out.println("This is my message " + message);
            mailSender.send(message);
        } catch (MessagingException e) {
            System.err.println("Error sending email: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
