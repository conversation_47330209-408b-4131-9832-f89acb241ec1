# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/
.angular/


# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv




# Environment and config
.env
.env.prod
.dockerignore
*.swp

# Docker-related
Dockerfile
docker-compose.yml
docker-compose.prod.yml
docker/
docker-browser.sh
quick-start-docker.sh

# Shell scripts
clean-dump.sh
connect-vm.sh
convert_dump_to_sql.sh
create_different_dump_formats.sh
deploy-to-vm.sh
device-identity-reset.sh
gcs_upload_and_fetch.sh
hardware-spoof.sh
quick-connect.sh
restore_dump_properly.sh
setup-meltano.sh
simple-deploy-test.sh
vm-deployment-script.sh
vm-setup.sh
vm_cleanup.sh
vpn-proxy-setup.sh

# SQL and DB dumps
*.dump
*.bkp
*.sql
oauth_migration.sql
create_leads_request_details_table.sql
DATABASE_DUMP_INFO.md

# Backup files
*.backup
backend/capsAllocation/src/main/java/com/vbs/capsAllocation/config/SecurityConfig.java.backup
backend/capsAllocation/data/employee-requests.json.backup

# Media and test data
TestVideo-*
ops-excellence-*.json

# Role templates
*_Role_Template.csv

# Docs and readmes
README.md
SOLUTION_README.md
VM-CONNECTION-README.md

# Build output
build/

# Local/dev/test data
org-chart/src/assets/env.docker.js
teamsphere-test-automation/

