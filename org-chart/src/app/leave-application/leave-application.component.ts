import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { NotificationService } from 'src/app/shared/notification.service';
import { distinctUntilChanged } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { LeaveService } from '../services/leave.service';
import { formatDate } from '@angular/common';
import { AlertDialogComponent } from './alert-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { ChangeDetectorRef } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AttendanceComponent } from './attendance/attendance.component';
import { NewRequestComponent } from '../leave-application/components/new-request/new-request.component';
import { HistoryComponent } from '../leave-application/components/history/history.component';
import { EmployeeService } from '../employee.service';


interface SelectOption {
  value: string;
  label: string;
}

export interface VunnoMgmtDto {
  ldap: string;
  name: string;
  role: string;
  email: string;
  programAlignment: string;
  team: string;
  lead: string;
}

interface LeaveRecord {
  status: string;  // Changed from union type to string
  applicationType: string,
  type: string;
  startDate: Date;
  endDate: Date;
  duration: string;
  approver: string;
}

@Component({
  selector: 'app-leave-application',
  templateUrl: './leave-application.component.html',
  styleUrls: ['./leave-application.component.css']
})

export class LeaveApplicationComponent implements OnInit {

  activeTab: string = 'attendance'; // Default active tab
  ldapProvided: boolean = false;
  loading = false;
  loadingMessage = "";
  submissionMessage: string = '';
  showSuccess: boolean = false;
  leaveForm!: FormGroup;
  submitted = false;
  requestorName = 'vrajoriya'; // For Testing Purposes
  requestorEmail = '<EMAIL>'; // For Testing Purposes
  approverEmail = '<EMAIL>'; // For Testing Purposes
  leaveBalance = { sick: 0, casual: 0, earned: 0, total: 0, totalwfh: 0.0, qtrwfh: 0.0, }; // Initial Values if Data does not come for some reason.
  leaveHistory: LeaveRecord[] = [];
  userRole: string | undefined;
  status = "PENDING";
  // snackBar: any;

  showApproverDropdown = false;
  approverOptions: string[] = [];


  @ViewChild('attendanceComp') attendanceComponent?: AttendanceComponent;
  @ViewChild(NewRequestComponent) newRequestComponent?: NewRequestComponent;
  @ViewChild(HistoryComponent) historyComponent?: HistoryComponent;

  constructor(private fb: FormBuilder, private leaveService: LeaveService, private dialog: MatDialog, private cdRef: ChangeDetectorRef, private snackbar: MatSnackBar,
    private notificationService: NotificationService, private employeeService:EmployeeService
  ) { }

  private markFormGroupTouched(formGroup: FormGroup | FormControl): void {
    if (formGroup instanceof FormControl) {
      formGroup.markAsTouched();
      return;
    }

    Object.values(formGroup.controls).forEach(control => {
      if (control instanceof FormGroup || control instanceof FormControl) {
        this.markFormGroupTouched(control);
      }
    });
  }

  isRoleAllowed(): boolean {
    const allowedRoles = ['LEAD', 'MANAGER', 'ADMIN_OPS_MANAGER'];
    return this.userRole !== undefined && allowedRoles.includes(this.userRole);
  }

  // Update your setActiveTab method
  setActiveTab(tab: string): void {
    this.activeTab = tab;

    // Load data for specific tabs
    if (tab === 'history') {
      // this.fetchLeaveHistory();
    } else if (tab === 'balance') {
      this.fetchLeaveBalance();
    } else if (tab === 'attendance') {
      setTimeout(() => {
        this.attendanceComponent?.refreshAttendance();
      }, 0);
    }
  }


  // Options for select fields
  requestTypeOptions: SelectOption[] = [
    { value: 'Leave', label: 'Leave' },
    { value: 'Work From Home', label: 'Work From Home' }
  ];

  leaveTypeOptions: SelectOption[] = [];

  durationTypeOptions: SelectOption[] = [
    { value: 'Full Day', label: 'Full Day' },
    { value: 'Half Day AM', label: 'Half Day (AM)' },
    { value: 'Half Day PM', label: 'Half Day (PM)' },
    { value: 'Multiple Days', label: 'Multiple Days' },
  ];


  private showLdapRequiredAlert(): void {
    this.dialog.open(AlertDialogComponent, {
      data: {
        title: 'LDAP Required',
        message: 'Please enter your LDAP in the New Request tab first'
      }
    });
  }

  private showSuccessNotification(): void {
    this.snackbar.open('Request submitted successfully!', 'Close', {
      duration: 5000,
      panelClass: ['custom-success-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top',
    });
  }

  private showErrorNotification(message: string): void {
    this.snackbar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top',
    });
  }

  // Change these properties
  currentPage = 1;
  itemsPerPage = 5;
  totalItems = 0; // Initialize to 0, will be set by API response

  get paginatedRecords(): LeaveRecord[] {
    return this.leaveHistory.slice(
      (this.currentPage - 1) * this.itemsPerPage,
      this.currentPage * this.itemsPerPage
    );
  }

  get totalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage);
  }

  ngOnInit(): void {
    // this.employeeService.getEmployees().subscribe({
    //   next: (employeesResponse) => {
    //     console.log('Employees fetched: User cannot call this API', employeesResponse.data); 
    //   },
    //   error: (err) => {
    //     console.error('Error fetching employees:', err);
    //   }
    // });
    this.userRole = localStorage.getItem('role') || undefined;
    this.initForm();

    this.leaveService.getCurrentUserLdap().subscribe({
      next: (response) => {
        const ldap = response.data.ldap;
        this.leaveForm.get('ldap')?.setValue(ldap);
        this.fetchManager();
      },
      error: (err) => console.error('Error fetching current user:', err)
    });

    this.setupFormSubscriptions();
    this.fetchLeaveBalance();
    // this.fetchLeaveHistory();
  }



  fetchLeaveBalance(): void {
    if (this.activeTab !== 'balance') return;
    const ldap = this.leaveForm.get('ldap')?.value;

    this.leaveService.getLeaveDetails(ldap).subscribe({
      next: (data) => {
        this.leaveBalance.sick = data[0];
        this.leaveBalance.casual = data[1];
        this.leaveBalance.earned = data[2];
        this.leaveBalance.total = data[3];
        this.leaveBalance.totalwfh = data[4];
        this.leaveBalance.qtrwfh = data[5];
      },
      error: (error) => {
        console.error('Error fetching leave details:', error);
        this.notificationService.showNotification({
          type: 'error',
          message: 'Leave Balance not available for now.Please try later.'
        })
      }
    });
  }

  async pasteFromClipboard(controlName: string) {
    try {
      const text = await navigator.clipboard.readText();
      this.leaveForm.get(controlName)?.setValue(text);
    } catch (err) {
      console.error('Failed to read clipboard contents: ', err);
      // Fallback for browsers that don't support clipboard API
      prompt('Paste your screenshot link here:', '');
    }
  }


  // Helper function to normalize status to the allowed values


  // onPageChange(page: number): void {
  //   this.currentPage = page;
  // }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Approved': return 'status-approved';
      case 'Pending': return 'status-pending';
      case 'Rejected': return 'status-rejected';
      default: return '';
    }
  }

  formatDate(date: Date): string {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  }

  getPageNumbers(): number[] {
    // Show max 3 page numbers around current page
    const maxVisible = 3;
    let start = Math.max(1, this.currentPage - 1);
    let end = Math.min(this.totalPages, start + maxVisible - 1);

    // Adjust if we're at the end
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getShowingRange(): string {
    const start = (this.currentPage - 1) * this.itemsPerPage + 1;
    const end = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
    return `Showing ${start} to ${end} of ${this.totalItems} entries`;
  }

  getShowingText(): string {
    return `Showing ${this.paginatedRecords.length} of ${this.totalItems} entries`;
  }



  goToFirstPage(): void {
    this.currentPage = 1;
  }

  goToLastPage(): void {
    this.currentPage = this.totalPages;
  }

  // Keep your existing onPageChange method
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  get f() {
    return this.leaveForm.controls;
  }


  private setupFormSubscriptions(): void {
    // LDAP changes
    this.leaveForm.get('ldap')?.valueChanges.subscribe(ldap => {
      this.ldapProvided = !!ldap && ldap.trim() !== '';
      this.updateApproverFieldState(ldap);
    });

    // Duration type changes - use distinctUntilChanged to prevent multiple calls
    this.leaveForm.get('durationType')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(value => {
      console.log("Duration type changed to:", value);
      this.updateTimes(value);
      this.updateEndDateRequirement(value);
    });

    // Request type changes
    this.leaveForm.get('requestType')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(requestType => {
      this.updateLeaveTypes(requestType);
      this.updateUIBasedOnRequestType(requestType);
    });

    // Start date changes
    this.leaveForm.get('startDate')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(startDate => {
      const durationType = this.leaveForm.get('durationType')?.value;
      if (startDate && (durationType === 'Full Day' || durationType === 'Half Day AM' || durationType === 'Half Day PM')) {
        this.leaveForm.get('endDate')?.setValue(startDate);
      }
    });

    // Leave type changes
    this.leaveForm.get('leaveType')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(value => {
      if (value === 'Long Leave') {
        this.leaveForm.get('durationType')?.setValue('Multiple Days');
        this.leaveForm.get('durationType')?.disable();
        this.leaveForm.get('endDate')?.setValidators([Validators.required]);
      } else {
        this.leaveForm.get('durationType')?.enable();
        this.leaveForm.get('endDate')?.clearValidators();
      }
      this.leaveForm.get('endDate')?.updateValueAndValidity();
    });
  }

  private initForm(): void {
    this.leaveForm = this.fb.group({
      ldap: ['', Validators.required], // Make disabled
      approver: ['', Validators.required],
      requestType: ['', Validators.required],
      leaveType: ['', Validators.required],
      durationType: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: [''],
      backupInfo: ['', Validators.required],
      oooProof: ['', Validators.required],
      timesheetProof: ['', Validators.required],
      startTime: ['07:30', Validators.required],
      endTime: ['04:30', Validators.required]
    }, { validators: this.dateRangeValidator });

    // Special handling for Long Leave
    this.leaveForm.get('leaveType')?.valueChanges.subscribe(value => {
      if (value === 'Long Leave') {
        this.leaveForm.get('durationType')?.setValue('Multiple Days');
        this.leaveForm.get('durationType')?.disable();
        this.leaveForm.get('endDate')?.setValidators([Validators.required]);
      } else {
        this.leaveForm.get('durationType')?.enable();
        this.leaveForm.get('endDate')?.clearValidators();
        this.leaveForm.get('endDate')?.setValidators([Validators.required]);
      }
      this.leaveForm.get('endDate')?.updateValueAndValidity();
    });

    this.setupFormSubscriptions();
  }

  dateRangeValidator(formGroup: FormGroup) {
    const startDate = formGroup.get('startDate')?.value;
    const endDate = formGroup.get('endDate')?.value;
    const durationType = formGroup.get('durationType')?.value;

    if (!startDate || !endDate) {
      return null;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Start date cannot be greater than end date
    if (start > end) {
      return { dateRange: 'Start date cannot be after end date' };
    }

    // For Multiple Days duration, dates cannot be equal
    if (durationType === 'Multiple Days' && start.getTime() === end.getTime()) {
      return { dateRange: 'For Multiple Days duration, start and end dates cannot be the same' };
    }

    return null;
  }

  updateLeaveTypes(requestType: string): void {
    if (requestType === 'Work From Home') {
      this.leaveForm.get('leaveType')?.disable();
    }
    else {
      this.leaveTypeOptions = [
        { value: 'Sick Leave', label: 'Sick Leave' },
        { value: 'Personal Leave', label: 'Personal Leave' },
        { value: 'Casual Leave', label: 'Casual Leave' },
        { value: 'Long Leave', label: 'Long Leave' },
      ];
      this.leaveForm.get('leaveType')?.enable();
    }
    this.leaveForm.get('leaveType')?.setValue('');
  }

  updateApproverFieldState(ldap: string): void {
    const approverControl = this.leaveForm.get('approver');

    if (!ldap || ldap.trim() === '') {
      approverControl?.disable();
      approverControl?.reset(); // Clear the value when disabled
    } else {
      approverControl?.enable();
    }
  }

  fetchManager(): void {
    const ldap = this.leaveForm.get('ldap')?.value;
    if (ldap) {
      this.leaveService.getManagerByLdap(ldap).subscribe({
        next: (managerDetails) => {
          if (managerDetails) {
            console.log("Manager details ", managerDetails);
            // Set the approver to the manager from the response
            this.leaveForm.get('approver')?.setValue(managerDetails[0].lead);

            // Update approver options (in case you want to keep this for future use)
            this.approverOptions = [managerDetails[0].lead];

            // Since we're directly setting the approver, we don't need a dropdown
            // Share with other components
            this.leaveService.setManagerDetails(managerDetails[0]);

          }
        },
        error: (err) => {
          console.error('Error fetching manager:', err);
          this.loading = false;
        }
      });
    }
  }



  private showError(message: string): void {
    // Implement your preferred error display mechanism
    alert(message); // Simple implementation - consider using a toast or snackbar
  }

  updateUIBasedOnRequestTypeO(requestType: string): void {
    if (requestType === 'Work From Home') {
      // Disable Leave Type
      this.leaveForm.get('leaveType')?.disable();

      // Disable the three fields
      this.leaveForm.get('oooProof')?.disable();
      this.leaveForm.get('timesheetProof')?.disable();
      this.leaveForm.get('backupInfo')?.disable();

      this.leaveForm.get('durationType')?.setValidators([Validators.required]);

      // Clear the values
      this.leaveForm.get('oooProof')?.setValue('');
      this.leaveForm.get('timesheetProof')?.setValue('');
      this.leaveForm.get('backupInfo')?.setValue('');

      // Remove validators for these fields
      this.leaveForm.get('oooProof')?.clearValidators();
      this.leaveForm.get('timesheetProof')?.clearValidators();
      this.leaveForm.get('backupInfo')?.clearValidators();

      // Update validity
      this.leaveForm.get('oooProof')?.updateValueAndValidity();
      this.leaveForm.get('timesheetProof')?.updateValueAndValidity();
      this.leaveForm.get('backupInfo')?.updateValueAndValidity();
    } else {
      // Enable Leave Type
      this.leaveForm.get('leaveType')?.enable();

      this.leaveForm.get('durationType')?.setValidators([Validators.required]);

      // Enable the three fields
      this.leaveForm.get('oooProof')?.enable();
      this.leaveForm.get('timesheetProof')?.enable();
      this.leaveForm.get('backupInfo')?.enable();

      // Add back validators
      this.leaveForm.get('oooProof')?.setValidators([Validators.required]);
      this.leaveForm.get('timesheetProof')?.setValidators([Validators.required]);
      this.leaveForm.get('backupInfo')?.setValidators([Validators.required]);

      // Update validity
      this.leaveForm.get('oooProof')?.updateValueAndValidity();
      this.leaveForm.get('timesheetProof')?.updateValueAndValidity();
      this.leaveForm.get('backupInfo')?.updateValueAndValidity();
    }
  }

  updateUIBasedOnRequestType(requestType: string): void {
    const isWFH = requestType === 'Work From Home';

    // Toggle Leave Type
    const leaveTypeControl = this.leaveForm.get('leaveType');
    if (isWFH) {
      leaveTypeControl?.disable();
      leaveTypeControl?.setValue('');
    } else {
      leaveTypeControl?.enable();
    }

    // Toggle the three fields
    const fieldsToToggle = ['oooProof', 'timesheetProof', 'backupInfo'];
    fieldsToToggle.forEach(field => {
      const control = this.leaveForm.get(field);
      if (isWFH) {
        control?.disable();
        control?.setValue('');
        control?.clearValidators();
      } else {
        control?.enable();
        control?.setValidators([Validators.required]);
      }
      control?.updateValueAndValidity();
    });

    // Always require duration type
    this.leaveForm.get('durationType')?.setValidators([Validators.required]);
    this.leaveForm.get('durationType')?.updateValueAndValidity();
  }

  get isWFH(): boolean {
    return this.leaveForm.get('requestType')?.value === 'Work From Home';
  }

  onRequestTypeChange(): void {
    const requestType = this.leaveForm.get('requestType')?.value;

    if (requestType === 'Work From Home') {
      this.leaveForm.get('leaveType')?.setValue('');
      this.leaveForm.get('leaveType')?.disable(); // Disable Leave Type
    } else {
      this.leaveForm.get('leaveType')?.enable(); // Enable Leave Type if not WFH
    }
    this.updateUIBasedOnRequestType(requestType);
  }

  updateTimes(durationType: string): void {
    // Set default times based on duration type
    if (durationType === 'Half Day AM') {
      this.leaveForm.get('startTime')?.setValue('07:30');
      this.leaveForm.get('endTime')?.setValue('12:00');
    } else if (durationType === 'Half Day PM') {
      this.leaveForm.get('startTime')?.setValue('12:00');
      this.leaveForm.get('endTime')?.setValue('16:30');
    } else if (durationType === 'Full Day') {
      this.leaveForm.get('startTime')?.setValue('07:30');
      this.leaveForm.get('endTime')?.setValue('16:30');
    }
    // else {
    //   this.leaveForm.get('startTime')?.setValue('07:30');
    //   this.leaveForm.get('endTime')?.setValue('04:30');
    // }
  }

  updateEndDateRequirement2(durationType: string): void {

    const requestType = this.leaveForm.get('requestType')?.value;
    const leaveType = this.leaveForm.get('leaveType')?.value;
    const durationTypes = this.leaveForm.get('durationType')?.value;
    const startDate = this.leaveForm.get('startDate')?.value;

    // Cases where end date is required
    if (durationType === 'Multiple Days' ||
      (requestType === 'Leave' && leaveType === 'Long Leave')) {
      this.leaveForm.get('endDate')?.setValidators([Validators.required]);
    }
    else if (durationType === 'Full Day' || durationType === 'Half Day AM' || durationType === 'Half Day PM') {
      this.leaveForm.get('endDate')?.setValue(startDate);

    }
    else {
      this.leaveForm.get('endDate')?.clearValidators();
    }
    this.leaveForm.get('endDate')?.updateValueAndValidity();
  }

  updateEndDateRequirement(durationType: string): void {
    const endDateControl = this.leaveForm.get('endDate');
    const startDate = this.leaveForm.get('startDate')?.value;

    if (!startDate) return;

    if (durationType === 'Multiple Days' ||
      (this.leaveForm.get('leaveType')?.value === 'Long Leave' &&
        this.leaveForm.get('requestType')?.value === 'Leave')) {
      endDateControl?.setValidators([Validators.required]);
    }
    else if (['Full Day', 'Half Day AM', 'Half Day PM'].includes(durationType)) {
      endDateControl?.setValue(startDate);
      endDateControl?.clearValidators();
    }
    else {
      endDateControl?.clearValidators();
    }

    endDateControl?.updateValueAndValidity();
  }

  onDurationTypeChange(): void {
    console.log("onDurationTypeChange function started");
    const durationType = this.leaveForm.get('durationType')?.value;
    this.updateTimes(durationType);
    this.updateEndDateRequirement(durationType);


    console.log("onDurationTypeChange function ended");
  }

  updateEndDateRequirement1(): void {
    const requestType = this.leaveForm.get('requestType')?.value;
    const leaveType = this.leaveForm.get('leaveType')?.value;
    const durationType = this.leaveForm.get('durationType')?.value;

    // Cases where end date is required
    if (durationType === 'Multiple Days' ||
      (requestType === 'Leave' && leaveType === 'Long Leave')) {
      this.leaveForm.get('endDate')?.setValidators([Validators.required]);
    } else {
      this.leaveForm.get('endDate')?.clearValidators();
    }

    this.leaveForm.get('endDate')?.updateValueAndValidity();
  }

  onSubmit(): void {
    this.submitted = true;
    this.markFormGroupTouched(this.leaveForm);

    // Force validation update
    const requestType = this.leaveForm.get('requestType')?.value;
    const durationValue = this.leaveForm.get('durationType')?.value;
    this.updateUIBasedOnRequestType(requestType);
    this.updateEndDateRequirement(durationValue);
    this.leaveForm.updateValueAndValidity(durationValue);

    if (this.leaveForm.invalid) {
      return;
    }

    this.submissionMessage = '';
    this.showSuccess = false;


    const formData = {
      ldap: this.leaveForm.value.ldap,
      approvingLead: this.leaveForm.value.approver,
      applicationType: this.leaveForm.value.requestType,
      leaveType: this.leaveForm.value.leaveType || "",
      lvWfhDuration: this.leaveForm.value.durationType,
      startDate: formatDate(this.leaveForm.value.startDate, 'yyyy-MM-dd', 'en-US'),
      endDate: formatDate(this.leaveForm.value.endDate, 'yyyy-MM-dd', 'en-US'),
      startDateTime: this.leaveForm.value.startTime,
      endDateTime: this.leaveForm.value.endTime,
      oooProof: this.getProofValue(this.leaveForm.value.oooProof),
      timesheetProof: this.getProofValue(this.leaveForm.value.timesheetProof),
      backupInfo: this.getProofValue(this.leaveForm.value.backupInfo),
      status: this.status
    };

    this.leaveService.submitLeaveRequest(formData).subscribe({
      next: (response) => {
        if (response?.message?.includes('Requested leave successfully saved into database')) {
          this.notificationService.showNotification({
            type: 'success',
            message: 'Request updated successfully.'
          });
          this.resetForm();
        }
      },
      error: (error) => {
        console.error('Submission error:', error);
        this.notificationService.showNotification({
          type: 'error',
          message: 'Leave request not successfull, Please Try Again!'
        });
      }
    });
  }

  private getProofValue(value: any): string {
    if (value === null || value === undefined || value === '') {
      return "NA,(WFH)";
    }
    return value;
  }

  resetForm(): void {
    // Clear only the fields that should be reset
    this.leaveForm.patchValue({
      requestType: '',
      leaveType: '',
      durationType: '',
      startDate: '',
      endDate: '',
      backupInfo: '',
      oooProof: '',
      timesheetProof: '',
      startTime: '07:30',
      endTime: '04:30'
    });

    // Clear specific fields that shouldn't keep values
    this.leaveForm.get('requestType')?.setValue('');
    this.leaveForm.get('leaveType')?.setValue('');
    this.leaveForm.get('durationType')?.setValue('');
    this.leaveForm.get('startDate')?.setValue('');
    this.leaveForm.get('endDate')?.setValue('');

    // Reset form state
    this.leaveForm.markAsPristine();
    this.leaveForm.markAsUntouched();
    this.submitted = false;
    this.loading = false;
    this.leaveTypeOptions = []
    this.submitted = false;
    this.cdRef.detectChanges();

    // Rebuild leaveTypeOptions if needed
    // this.updateLeaveTypeOptions();

    // Reset form state without affecting values
    // this.leaveForm.markAsPristine();
    // this.leaveForm.markAsUntouched();

    // this.submitted = false;
    // this.loading = false;
    // this.leaveForm.enable();
    // this.leaveTypeOptions = [];
  }

}