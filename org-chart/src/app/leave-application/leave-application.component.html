<div class="form-container">
  <app-page-header title="Attendance and Leave/Work from Home Application"
    subtitle="Apply for leave and work from home">
  </app-page-header>

  <!-- Tabs Section -->
  <div class="tabs">

    <div class="tab" [class.active]="activeTab === 'attendance'" (click)="setActiveTab('attendance')">
      Attendance
    </div>

    <div class="tab" [class.active]="activeTab === 'new'" (click)="setActiveTab('new')">
      New Request
    </div>

    <div class="tab" [class.active]="activeTab === 'balance'" (click)="setActiveTab('balance')">
      Balance
    </div>
    
    <div class="tab" [class.active]="activeTab === 'history'" (click)="setActiveTab('history')">
      History
    </div>

    <div class="tab" [class.active]="activeTab === 'requests'"
      (click)="setActiveTab('requests')"
      *ngIf="isRoleAllowed()"
      >
      Requests
    </div>
    <!-- *ngIf="isUploadAllowed()" -->
  </div>

  <app-attendance [hidden]="activeTab !== 'attendance'"></app-attendance>

  <app-balance 
  *ngIf="activeTab === 'balance'"
  [leaveBalance]="leaveBalance" 
  [canUpload]="isRoleAllowed()" 
  [userRole]="userRole">
  </app-balance>

  <app-history
    *ngIf="activeTab === 'history'"
    [userRole]="userRole"
    [ldap]="leaveForm.get('ldap')?.value">
  </app-history>

  <app-requests *ngIf="activeTab === 'requests'"></app-requests>



  <!-- <div *ngIf="activeTab === 'requests'" class="requests-tab">
    <div *ngIf="activeTab === 'requests'" class="tab-content">
      <h2>Pending Requests</h2>
      <div class="table-responsive">
        <table class="leave-history-table mat-elevation-z8">
          <thead>
            <tr>
              <th>Status</th>
              <th>Request Type</th>
              <th>Leave Type</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Duration</th>
              <th>Requestor</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <span class="">
                  Request Status
                </span>
              </td>
              <td>applicationType</td>
              <td>type</td>
              <td>startDate</td>
              <td>endDate</td>
              <td>Durationtd>
              <td>Ldap</td>
              <td class="flex gap-3 justify-center">
                <button class="text-sm text-purple-600 hover:underline">
                  Approve
                </button>
                <button class="text-sm text-red-600 hover:underline">
                  Deny
                </button>
              </td>

            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div> -->

  <!-- New Request Tab Content -->
  <form [formGroup]="leaveForm" (ngSubmit)="onSubmit()" *ngIf="activeTab === 'new'" class="tab-content">
    <h2>Apply for Leave/WFH</h2>

    <!-- First Row of Fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Ldap</mat-label>
        <input matInput formControlName="ldap" (blur)="fetchManager()" readonly>
        <mat-error *ngIf="submitted && f['ldap'].errors?.['required']">
          Ldap is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Approving Lead/Manager</mat-label>
        <input matInput formControlName="approver" readonly>
        <mat-error *ngIf="submitted && f['approver'].errors?.['required']">
          Approving Lead/Manager is required
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Second Row of Fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Request Type</mat-label>
        <mat-select formControlName="requestType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of requestTypeOptions" [value]="option.value">
            {{option.label}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="submitted && f['requestType'].errors?.['required']">
          Request type is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Leave Type</mat-label>
        <mat-select formControlName="leaveType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of leaveTypeOptions" [value]="option.value">
            {{option.label}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="submitted && f['leaveType'].errors?.['required']">
          Leave type is required
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Third Row of Fields - Duration takes full width -->
    <div class="form-row full-width">
      <mat-form-field appearance="outline" class="full-width-field">
        <mat-label>Duration</mat-label>
        <mat-select formControlName="durationType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of durationTypeOptions" [value]="option.value">
            {{option.label}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="submitted && f['durationType'].errors?.['required']">
          Duration is required
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Fourth Row of Fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Start Date</mat-label>
        <input matInput [matDatepicker]="startDatePicker" formControlName="startDate" readonly>
        <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #startDatePicker></mat-datepicker>
        <mat-error *ngIf="submitted && f['startDate'].errors?.['required']">
          Start date is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>End Date</mat-label>
        <input matInput [matDatepicker]="endDatePicker" formControlName="endDate" readonly>
        <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #endDatePicker></mat-datepicker>
        <mat-error *ngIf="submitted && f['endDate'].errors?.['required']">
          End date is required
        </mat-error>
        <mat-error *ngIf="submitted && leaveForm.errors?.['dateRange']">
          End date must be after start date
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Fifth Row of Fields -->
    <div class="form-row">
      <mat-form-field appearance="outline" [class.disabled-field]="isWFH">
        <mat-label>OOO Screenshot Link</mat-label>
        <input matInput formControlName="oooProof" placeholder="Paste OOO screenshot link (Ctrl+V)">
        <mat-error *ngIf="submitted && f['oooProof'].errors?.['required']">
          Please provide OOO snip
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" [class.disabled-field]="isWFH">
        <mat-label>Timesheet Screenshot Link</mat-label>
        <input matInput formControlName="timesheetProof" placeholder="Paste timesheet screenshot link (Ctrl+V)">
        <mat-error *ngIf="submitted && f['timesheetProof'].errors?.['required']">
          Please provide timesheet snip
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Sixth Row of Fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Start Time</mat-label>
        <input matInput type="time" formControlName="startTime">
        <mat-error *ngIf="submitted && f['startTime'].errors?.['required']">
          Start time is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>End Time</mat-label>
        <input matInput type="time" formControlName="endTime">
        <mat-error *ngIf="submitted && f['endTime'].errors?.['required']">
          End time is required
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Seventh Row of Fields - Backup Info takes full width -->
    <div class="form-row full-width">
      <mat-form-field appearance="outline" [class.disabled-field]="isWFH" class="full-width-field">
        <mat-label>Backup Info</mat-label>
        <textarea matInput formControlName="backupInfo" rows="4"
          placeholder="Enter backup person details (name, contact, responsibilities)"></textarea>
        <mat-error *ngIf="submitted && f['backupInfo'].errors?.['required']">
          Backup Info is required
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Buttons -->
    <div class="buttons">
      <button mat-raised-button color="warn" class="action-button cancel-button" type="button" (click)="resetForm()">
        Cancel
      </button>
      <button mat-raised-button color="primary" class="action-button save-button" type="submit">
        Submit Request
      </button>
    </div>
  </form>
</div>