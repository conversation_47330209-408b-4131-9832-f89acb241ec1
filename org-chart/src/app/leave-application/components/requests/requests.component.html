<mat-tab-group>
  <!-- PENDING TAB -->

  <mat-tab label="Processed Requests">
    <div class="processed-requests-container">

      <!-- Filter Chips -->
      <mat-button-toggle-group [value]="selectedStatus" (change)="onStatusFilterChange($event.value)"
        appearance="legacy" exclusive>
        <mat-button-toggle *ngFor="let status of processedStatuses" [value]="status">
          {{ status }}
        </mat-button-toggle>
      </mat-button-toggle-group>


      <!-- Table -->
      <mat-table [dataSource]="filteredProcessedDataSource" matSort class="mat-elevation-z8">

        <!-- LDAP -->
        <ng-container matColumnDef="ldap">
          <mat-header-cell *matHeaderCellDef mat-sort-header>LDAP</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.ldap }}</mat-cell>
        </ng-container>

        <!-- NAME -->
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Name</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.requestorName }}</mat-cell>
        </ng-container>

        <!-- REQUEST TYPE -->
        <ng-container matColumnDef="requestType">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Request Type</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.applicationType }}</mat-cell>
        </ng-container>

        <!-- LEAVE TYPE -->
        <ng-container matColumnDef="leaveType">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Leave Type</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.leaveType }}</mat-cell>
        </ng-container>

        <!-- START DATE -->
        <ng-container matColumnDef="startDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.startDate | date:'MMM d, y' }}</mat-cell>
        </ng-container>

        <!-- END DATE -->
        <ng-container matColumnDef="endDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header>End Date</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.endDate | date:'MMM d, y' }}</mat-cell>
        </ng-container>

        <!-- DURATION -->
        <ng-container matColumnDef="duration">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Duration</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.lvWfhDuration }}</mat-cell>
        </ng-container>

        <!-- STATUS -->
        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Status</mat-header-cell>
          <mat-cell *matCellDef="let row">
            <span [ngClass]="{
            'status-approved': row.status === 'APPROVED',
            'status-rejected': row.status === 'REJECTED',
            'status-revoked': row.status === 'REVOKED'
          }">{{ row.status }}</span>
          </mat-cell>
        </ng-container>

        <!-- ACTIONS -->
        <ng-container matColumnDef="actions">
          <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
          <mat-cell *matCellDef="let row">
            <button mat-raised-button color="warn" class="action-button revoke-button"
              [disabled]="isPastEndDate(row.endDate)" *ngIf="row.status === 'APPROVED'" (click)="revokeRequest(row)">
              Revoke
            </button>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </mat-table>

      <mat-paginator #processedPaginator [length]="filteredProcessedDataSource.data.length || 0" [pageSize]="10"
        showFirstLastButtons>
      </mat-paginator>
    </div>
  </mat-tab>

  <mat-tab label="Pending Requests">
    <div class="new-requests-table">
      <mat-table [dataSource]="pendingDataSource" matSort class="mat-elevation-z8">

        <!-- LDAP -->
        <ng-container matColumnDef="ldap">
          <mat-header-cell *matHeaderCellDef mat-sort-header>LDAP</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.ldap }}</mat-cell>
        </ng-container>

        <!-- NAME -->
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Name</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.requestorName }}</mat-cell>
        </ng-container>

        <!-- REQUEST TYPE -->
        <ng-container matColumnDef="requestType">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Request Type</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.applicationType }}</mat-cell>
        </ng-container>

        <!-- LEAVE TYPE -->
        <ng-container matColumnDef="leaveType">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Leave Type</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.leaveType }}</mat-cell>
        </ng-container>

        <!-- START DATE -->
        <ng-container matColumnDef="startDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.startDate | date:'MMM d, y' }}</mat-cell>
        </ng-container>

        <!-- END DATE -->
        <ng-container matColumnDef="endDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header>End Date</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.endDate | date:'MMM d, y' }}</mat-cell>
        </ng-container>

        <!-- DURATION -->
        <ng-container matColumnDef="duration">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Duration</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.lvWfhDuration }}</mat-cell>
        </ng-container>

        <!-- STATUS -->
        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Status</mat-header-cell>
          <mat-cell *matCellDef="let row">
            <span [ngClass]="{
                'status-pending': row.status === 'PENDING',
                'status-approved': row.status === 'APPROVED',
                'status-rejected': row.status === 'REJECTED'
              }">{{ row.status }}</span>
          </mat-cell>
        </ng-container>

        <!-- ACTIONS -->
        <ng-container matColumnDef="actions">
          <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
          <mat-cell *matCellDef="let row">
            <button mat-raised-button color="primary" class="action-button approve-button"
              *ngIf="row.status === 'PENDING'" (click)="approveRequest(row)">
              Approve
            </button>

            <button mat-raised-button color="warn" class="action-button delete-button" *ngIf="row.status === 'PENDING'"
              (click)="rejectRequest(row)">
              Reject
            </button>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </mat-table>

      <mat-paginator #pendingPaginator [length]="pendingDataSource?.data?.length || 0" [pageSize]="10"
        showFirstLastButtons>
      </mat-paginator>
    </div>
  </mat-tab>


</mat-tab-group>




<!-- APPROVED TAB -->
<!-- <mat-tab label="Approved Requests">
      <div class="new-requests-table">
        <mat-table [dataSource]="approvedDataSource" matSort class="mat-elevation-z8">
  
          <ng-container matColumnDef="ldap">
            <mat-header-cell *matHeaderCellDef mat-sort-header>LDAP</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.ldap }}</mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="name">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Name</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.requestorName }}</mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="requestType">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Request Type</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.applicationType }}</mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="leaveType">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Leave Type</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.leaveType }}</mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="startDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.startDate | date:'MMM d, y' }}</mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="endDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header>End Date</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.endDate | date:'MMM d, y' }}</mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="duration">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Duration</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.lvWfhDuration }}</mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="status">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Status</mat-header-cell>
            <mat-cell *matCellDef="let row">
              <span [ngClass]="{
                'status-pending': row.status === 'PENDING',
                'status-approved': row.status === 'APPROVED',
                'status-rejected': row.status === 'REJECTED'
              }">{{ row.status }}</span>
            </mat-cell>
          </ng-container>
  
          <ng-container matColumnDef="actions">
            <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
            <mat-cell *matCellDef="let row">
              <button mat-raised-button color="primary"
                      class="action-button revoke-button"
                      [disabled]="isPastEndDate(row.endDate)"
                      *ngIf="row.status === 'APPROVED'"
                      (click)="revokeRequest(row)">
                Revoke
              </button>
            </mat-cell>
          </ng-container>
  
          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>
  
        <mat-paginator #approvedPaginator [length]="approvedDataSource?.data?.length || 0"
                       [pageSize]="10" showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-tab> -->