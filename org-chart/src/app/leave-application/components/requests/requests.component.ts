import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { LeaveService } from 'src/app/services/leave.service';
import { NotificationService } from 'src/app/shared/notification.service';
import { MatChipsModule } from '@angular/material/chips';


@Component({
  selector: 'app-requests',
  templateUrl: './requests.component.html',
  styleUrls: ['./requests.component.css']
})
export class RequestsComponent implements OnInit {
  displayedColumns: string[] = [
    'ldap', 'name', 'requestType', 'leaveType',
    'startDate', 'endDate', 'duration', 'status', 'actions'
  ];

  processedDataSource: MatTableDataSource<any> = new MatTableDataSource();
  selectedProcessedStatus: string = 'APPROVED'; // Default filter
  processedRequests: any[] = []; // Raw data


  // Tab DataSources
  pendingDataSource: MatTableDataSource<any> = new MatTableDataSource();
  filteredProcessedDataSource: MatTableDataSource<any> = new MatTableDataSource();

  // Filters
  processedStatuses: string[] = ['APPROVED', 'REJECTED', 'REVOKED'];
  selectedStatus: string = 'APPROVED';

  // ViewChilds
  @ViewChild('pendingPaginator') pendingPaginator!: MatPaginator;
  @ViewChild('processedPaginator') processedPaginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private leaveService: LeaveService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.fetchPendingRequests();
    this.fetchProcessedRequests(); 
  }

  ngAfterViewInit(): void {
    this.processedDataSource.paginator = this.processedPaginator;
    this.processedDataSource.sort = this.sort;
  }
  

  fetchPendingRequests(): void {
    this.leaveService.getPendingRequestsForLead().subscribe({
      next: (requests) => {
        this.pendingDataSource = new MatTableDataSource(requests);
        this.pendingDataSource.paginator = this.pendingPaginator;
        this.pendingDataSource.sort = this.sort;
      },
      error: () => {
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to fetch pending requests.'
        });
      }
    });
  }

  fetchProcessedRequests(): void {
    this.leaveService.getProcessedRequestsForLead().subscribe({
      next: (requests) => {
        this.processedRequests = requests || [];
        this.onStatusFilterChange(this.selectedProcessedStatus); 
      },
      error: () => {
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to fetch processed requests.'
        });
      }
    });
  }

  applyProcessedStatusFilter(): void {
    const filtered = this.processedRequests.filter(r => r.status === this.selectedProcessedStatus);
    this.processedDataSource = new MatTableDataSource(filtered);
    this.processedDataSource.paginator = this.processedPaginator;
    this.processedDataSource.sort = this.sort;
  }
  
  
  onProcessedStatusChange(status: string): void {
    this.selectedProcessedStatus = status;
    this.applyProcessedStatusFilter();
  }  

  // === Filtering Logic for Processed Tab ===
  onStatusFilterChange(status: string) {
    this.selectedStatus = status;
    this.filteredProcessedDataSource.data = this.processedRequests.filter(
      (req) => req.status === status
    );
  }

  applyFilter(status: string): void {
    const filtered = this.processedDataSource.data.filter(
      (request) => request.status === status
    );
    this.processedDataSource.data = filtered;
    this.filteredProcessedDataSource.paginator = this.processedPaginator;
    this.filteredProcessedDataSource.sort = this.sort;
  }

  approveRequest(request: any): void {
    this.leaveService.approveRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request approved.'
        });
        this.fetchPendingRequests();
        this.fetchProcessedRequests(); // also update processed
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to approve request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  rejectRequest(request: any): void {
    this.leaveService.rejectRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request rejected.'
        });
        this.fetchPendingRequests();
        this.fetchProcessedRequests();
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to reject request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  revokeRequest(request: any): void {
    this.leaveService.revokeRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request revoked.'
        });
        this.fetchProcessedRequests();
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to revoke request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  isPastEndDate(endDate: string): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const leaveEndDate = new Date(endDate);
    leaveEndDate.setHours(0, 0, 0, 0);
    return leaveEndDate < today;
  }
}

