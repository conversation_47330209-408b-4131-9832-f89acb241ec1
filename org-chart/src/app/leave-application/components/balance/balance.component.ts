import { Component, Input } from '@angular/core';
import { NotificationService } from 'src/app/shared/notification.service';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { LeaveService } from 'src/app/services/leave.service';

@Component({
  selector: 'app-balance',
  templateUrl: './balance.component.html',
  styleUrls: ['./balance.component.css']
})

export class BalanceComponent {
  @Input() leaveBalance: any;
  @Input() canUpload: boolean = false;
  @Input() userRole?: string;

  constructor(private fb: FormBuilder, private notificationService: NotificationService, private leaveService: LeaveService,
  ) { }

  selectedFile: File | null = null;

  googleSheetId: string = '';

  
onFileSelected(event: Event): void {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    this.selectedFile = input.files[0];
  }
}

uploadLeaveBalance(): void {
  if (!this.selectedFile) {
    this.notificationService.showNotification({
      type: 'error',
      message: 'Please select a file before uploading.'
    });
    return;
  }

  const formData = new FormData();
  formData.append("file", this.selectedFile);

  this.leaveService.uploadLeaveBalance(formData).subscribe({
    next: () => this.notificationService.showNotification({
      type: 'success',
      message: 'Leave details uploaded successfully!'
    }),
    error: () => this.notificationService.showNotification({
      type: 'error',
      message: 'Failed to upload leave balances.'
    })
  });
}
  
}