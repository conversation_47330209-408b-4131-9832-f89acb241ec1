import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatMenuTrigger } from '@angular/material/menu';
import { LateCheckinDialogComponent } from './late-checkin-dialog.component';
import { formatDate } from '@angular/common';
import { LeaveService } from 'src/app/services/leave.service';
import { NotificationService } from 'src/app/shared/notification.service';
import { AttendanceService } from 'src/app/services/attendance.service';
import { AttendanceRequest } from 'src/app/model/attendance-request.model';
import { filter, interval, Subject, take, takeUntil } from 'rxjs';
import { FormControl, FormGroup } from '@angular/forms';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { CheckInStatusResponse } from 'src/app/model/check-in-status.model';
import { MatTableDataSource } from '@angular/material/table';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

interface BaseResponse<T> {
  status: string;
  code: number;
  message: string;
  data: T;
}

interface RawAttendanceResponse {
  id: string;
  entryDate: string;
  entryTimestamp: string;
  lateLoginReason: string;
  isOutsideOffice: boolean;
  isDefaulter: boolean;
}

interface AttendanceRecord {
  id: string;
  date: string;
  checkIn: string;
  status: 'On Time' | 'Late';
  reason: string;
  isOutsideOffice: boolean;
  isDefaulter: boolean;
}

@Component({
  selector: 'app-attendance',
  templateUrl: './attendance.component.html',
  styleUrls: ['./attendance.component.css']
})
export class AttendanceComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = ['id', 'date', 'checkIn', 'status', 'reason', 'isOutsideOffice', 'isDefaulter'];
  showColumnFilters = false;
  baseUrl = environment.apiUrl;
  filterValues: any = {};
  private _attendanceRecords: AttendanceRecord[] = [];
  private destroy$ = new Subject<void>();
  checkInStatus: CheckInStatusResponse | null = null;

  dataSource = new MatTableDataSource<AttendanceRecord>([]);
  dateRange = new FormGroup({
    start: new FormControl<Date | null>(this.getLastWeekStartDate()),
    end: new FormControl<Date | null>(new Date())
  });
  
  // Column filtering properties
  columnUniqueValues: { [key: string]: string[] } = {};
  currentFilterMenuState = {
    columnKey: null as string | null,
    tempSelectedValues: [] as string[],
    searchText: ''
  };
  userRole: string | undefined;
  isCheckedIn = false;
  status = '';
  checkInTime = '';
  isLate = false;

  currentDate = formatDate(new Date(), 'EEEE, MMMM d, y', 'en-US');
  userInfo = {
    ldap: '',
    name: '',
    role: '',
    email: '',
    programAlignment: '',
    team: '',
    lead: '',
    manager: '',
    shift: ''
  };
  shiftDetails = {
    code: '',
    startTime:'',
    endTime: '',
    maxLoginTime: '',
  }
  latitude!: number;
  longitude!: number;
  checkedIn = false;
  checkedInTime = '';

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private http: HttpClient,
    public dialog: MatDialog,
    private leaveService: LeaveService,
    private notificationService: NotificationService,
    private attendanceService: AttendanceService,
    private cdr: ChangeDetectorRef,
  ) { }

  ngOnInit(): void {
    this.userRole = localStorage.getItem('role') || undefined;
    this.currentUser();

    this.dataSource.filterPredicate = (data: AttendanceRecord, filter: string) => {
      try {
        const filterObject = JSON.parse(filter);
        const keys = Object.keys(filterObject);

        if (keys.length === 0) return true;

        return keys.every(key => {
          const filterValues = filterObject[key];
          if (!filterValues || (Array.isArray(filterValues) && filterValues.length === 0)) {
            return true;
          }

          if (Array.isArray(filterValues)) {
            const dataValue = String(data[key as keyof AttendanceRecord] || '').toLowerCase();
            return filterValues.some(value => dataValue.includes(value.toLowerCase()));
          }

          const dataValue = String(data[key as keyof AttendanceRecord] || '').toLowerCase();
          return dataValue.includes(filterValues.toLowerCase());
        });
      } catch (error) {
        console.error('Error parsing filter:', error);
        return true;
      }
    };
  }

  hasCheckedInToday(): boolean {
    // Check both the checkInStatus and local records
    if (this.checkInStatus?.checkedIn) {
      return true;
    }

    const today = new Date().toISOString().split('T')[0];
    return this._attendanceRecords.some(record =>
      record.date === today
    );
  }


  getTodayCheckInTime(): string {
    // Check checkInStatus first for most up-to-date info
    if (this.checkInStatus?.checkedIn && this.checkInStatus.checkInTime) {
      return this.checkInStatus.checkInTime;
    }

    const today = new Date().toISOString().split('T')[0];
    const todayRecord = this._attendanceRecords.find(record =>
      record.date === today
    );
    return todayRecord ? todayRecord.checkIn : '--:--';
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  loadAttendanceRecords(ldap: string) {
    this.attendanceService.getAttendanceByLdap(ldap).subscribe({
      next: (records: any[]) => {
        this._attendanceRecords = records.map(record => ({
          id: record.id,
          date: formatDate(record.entryDate, 'yyyy-MM-dd', 'en-US'),
          checkIn: formatDate(record.entryTimestamp, 'HH:mm', 'en-US'),
          status: this.isLateCheckIn(record.entryTimestamp) ? 'Late' : 'On Time',
          reason: record.lateLoginReason || '-',
          isOutsideOffice: record.isOutsideOffice,
          isDefaulter: record.isDefaulter
        }));
        console.log("Attendance Records ", this._attendanceRecords)
        this._attendanceRecords.sort((a, b) =>
          new Date(b.date + ' ' + b.checkIn).getTime() - new Date(a.date + ' ' + a.checkIn).getTime()
        );

        this.dataSource.data = this._attendanceRecords;
        this.collectUniqueColumnValues(this._attendanceRecords);

        if (this.paginator) {
          this.paginator.firstPage();
        }
      },
      error: (err) => {
        console.error('Error loading attendance:', err);
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to load attendance records'
        });
      }
    });
  }

  loadAttendanceRecordsTemp() {
    const selectedStart = this.dateRange.get('start')?.value;
    const selectedEnd = this.dateRange.get('end')?.value;

    let startDate: string;
    let endDate: string | null = null;

    if (selectedStart && selectedEnd) {
      // If user has selected a date range via Apply button
      startDate = this.formatDate(selectedStart);
      endDate = this.formatDate(selectedEnd);
    } else if (this.userRole === 'LEAD' || this.userRole === 'MANAGER' || this.userRole === 'ADMIN_OPS_MANAGER') {
      // Default behavior for leads/managers/admins
      startDate = this.formatDate(new Date());
    } else {
      // Default for regular users: 1-week window
      startDate = this.formatDate(this.getLastWeekStartDate());
      endDate = this.formatDate(new Date());
    }

    // Construct URL
    let url = `${this.baseUrl}/api/atom?startDate=${startDate}`;
    if (endDate) {
      url += `&endDate=${endDate}`;
    }

    // API call
    this.http.get<BaseResponse<RawAttendanceResponse[]>>(url).subscribe({
      next: (response) => {
        const rawRecords = response.data;

        this._attendanceRecords = rawRecords.map(record => ({
          id: record.id,
          date: formatDate(record.entryDate, 'yyyy-MM-dd', 'en-US'),
          checkIn: formatDate(record.entryTimestamp, 'HH:mm', 'en-US'),
          status: this.isLateCheckIn(record.entryTimestamp) ? 'Late' : 'On Time',
          reason: record.lateLoginReason || '-',
          isOutsideOffice: record.isOutsideOffice,
          isDefaulter: record.isDefaulter
        }));

        console.log("Attendance Records", this._attendanceRecords);

        this._attendanceRecords.sort((a, b) =>
          new Date(b.date + ' ' + b.checkIn).getTime() -
          new Date(a.date + ' ' + a.checkIn).getTime()
        );

        this.dataSource.data = this._attendanceRecords;
        this.collectUniqueColumnValues(this._attendanceRecords);

        if (this.paginator) {
          this.paginator.firstPage();
        }
      },
      error: (err) => {
        console.error('Error loading attendance:', err);
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to load attendance records'
        });
      }
    });
  }

  // Column filter methods
  openFilterMenu(columnKey: string, trigger: MatMenuTrigger): void {
    this.currentFilterMenuState.columnKey = columnKey;
    this.currentFilterMenuState.tempSelectedValues =
      this.filterValues[columnKey] ? [...this.filterValues[columnKey]] : [];
    this.currentFilterMenuState.searchText = '';
  }

  isTempSelected(value: string): boolean {
    return this.currentFilterMenuState.tempSelectedValues.includes(value);
  }

  toggleTempSelection(value: string, checked: boolean): void {
    if (checked) {
      if (!this.isTempSelected(value)) {
        this.currentFilterMenuState.tempSelectedValues.push(value);
      }
    } else {
      const index = this.currentFilterMenuState.tempSelectedValues.indexOf(value);
      if (index >= 0) {
        this.currentFilterMenuState.tempSelectedValues.splice(index, 1);
      }
    }
  }

  isAllTempSelected(): boolean {
    const filteredOptions = this.getFilteredMenuOptions();
    return filteredOptions.length > 0 &&
      filteredOptions.every(value => this.isTempSelected(value));
  }

  isSomeTempSelected(): boolean {
    const filteredOptions = this.getFilteredMenuOptions();
    return filteredOptions.some(value => this.isTempSelected(value)) &&
      !this.isAllTempSelected();
  }

  toggleSelectAllTemp(checked: boolean): void {
    const filteredOptions = this.getFilteredMenuOptions();

    if (checked) {
      filteredOptions.forEach(value => {
        if (!this.isTempSelected(value)) {
          this.currentFilterMenuState.tempSelectedValues.push(value);
        }
      });
    } else {
      this.currentFilterMenuState.tempSelectedValues =
        this.currentFilterMenuState.tempSelectedValues.filter(
          value => !filteredOptions.includes(value)
        );
    }
  }

  onFilterApplied(): void {
    if (this.currentFilterMenuState.columnKey) {
      const key = this.currentFilterMenuState.columnKey;
      this.filterValues[key] = [...this.currentFilterMenuState.tempSelectedValues];
      this.applyColumnFilters();
    }
  }

  clearColumnFilter(): void {
    if (this.currentFilterMenuState.columnKey) {
      const key = this.currentFilterMenuState.columnKey;
      this.filterValues[key] = [];
      this.currentFilterMenuState.tempSelectedValues = [];
      this.applyColumnFilters();
    }
  }

  applyColumnFilters(): void {
    const activeFilters = Object.keys(this.filterValues).reduce((acc, key) => {
      if (this.filterValues[key]?.length > 0) {
        acc[key] = this.filterValues[key];
      }
      return acc;
    }, {} as { [key: string]: string[] });

    this.dataSource.filter = JSON.stringify(activeFilters);

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  getUniqueColumnValues(columnKey: string): string[] {
    return this.columnUniqueValues[columnKey] || [];
  }

  get filteredMenuOptions(): string[] {
    return this.getFilteredMenuOptions();
  }

  getFilteredMenuOptions(): string[] {
    if (!this.currentFilterMenuState.columnKey) return [];

    const columnKey = this.currentFilterMenuState.columnKey;
    const searchText = this.currentFilterMenuState.searchText.toLowerCase();
    const options = this.getUniqueColumnValues(columnKey);

    if (!searchText) return options;

    return options.filter(option =>
      option.toLowerCase().includes(searchText)
    ); // Removed extra parenthesis
  }

  isFilterActive(columnKey: string): boolean {
    return this.filterValues[columnKey]?.length > 0;
  }

  collectUniqueColumnValues(data: AttendanceRecord[]): void {
    this.columnUniqueValues = {};

    this.displayedColumns.forEach(column => {
      const uniqueValues = new Set<string>();

      data.forEach(record => {
        const value = record[column as keyof AttendanceRecord];
        if (value !== undefined && value !== null) {
          uniqueValues.add(String(value));
        }
      });

      this.columnUniqueValues[column] = Array.from(uniqueValues).sort();
    });
  }

  // Rest of your existing methods (checkIn, getUserLocation, etc.) remain the same
  // Only the implementation needs to be copied over

  private isLateCheckIn(timestamp: string | Date): boolean {
    if (!this.shiftDetails.maxLoginTime) return false; // Fallback, can't determine

    const checkInTime = new Date(timestamp);
    const [maxHour, maxMin] = this.shiftDetails.maxLoginTime.split(':').map(Number);

    return (
      checkInTime.getHours() > maxHour ||
      (checkInTime.getHours() === maxHour && checkInTime.getMinutes() > maxMin)
    );
  }


  async checkIn() {
    if (this.checkInStatus?.checkedIn || this.hasCheckedInToday()) {
      this.notificationService.showNotification({
        type: 'info',
        message: 'You have already checked in today'
      });
      return;
    }
  
    try {
      const location = await this.getUserLocation();
      const now = new Date();
  
      if (!this.shiftDetails) {
        this.notificationService.showNotification({
          type: 'error',
          message: 'Shift details not loaded. Please try again.'
        });
        return;
      }
  
      const { startTime, endTime, maxLoginTime } = this.shiftDetails;
  
      const nowMinutes = now.getHours() * 60 + now.getMinutes();
      const startMinutes = this.parseTimeToMinutes(startTime);
      const endMinutes = this.parseTimeToMinutes(endTime);
      const maxLoginMinutes = this.parseTimeToMinutes(maxLoginTime);
  
      const checkInTime = formatDate(now, 'HH:mm:ss', 'en-US');
  
      // Case 1: Shift mismatch
      if (nowMinutes < startMinutes || nowMinutes > endMinutes) {
        this.dialog.open(LateCheckinDialogComponent, {
          width: '500px',
          data: {
            checkInTime,
            maxLoginTime,
            isShiftMismatch: true,
            shiftCode: this.shiftDetails.code,
            shiftWindow: `${startTime} to ${endTime}`,
            title: 'Shift Mismatch Detected',
            message: `Your check-in time (${checkInTime}) is outside your scheduled shift (${startTime} to ${endTime}). Please provide a reason.`
          }
        }).afterClosed().subscribe(result => {
          if (result) {
            this.addRecord(checkInTime, 'Late', result.reason, result.notes);
          }
        });
  
        return;
      }
  
      // Case 2: Within shift, but late
      if (nowMinutes > maxLoginMinutes) {
        this.dialog.open(LateCheckinDialogComponent, {
          width: '500px',
          data: {
            checkInTime,
            maxLoginTime,
            isShiftMismatch: false,
            title: 'Late Check-In Recorded',
            message: `You've checked in after ${maxLoginTime}. Please select a reason for your late login.`
          }
        }).afterClosed().subscribe(result => {
          if (result) {
            this.addRecord(checkInTime, 'Late', result.reason, result.notes);
          }
        });
  
        return;
      }
  
      // Case 3: On time
      this.addRecord(checkInTime, 'On Time', '-', '');
  
    } catch (error) {
      this.handleCheckInError(error);
    }
  }
  
  
  
  

  private handleLateCheckIn(checkInTime: string) {
    const dialogRef = this.dialog.open(LateCheckinDialogComponent, {
      width: '500px',
      data: { checkInTime, maxLoginTime: this.shiftDetails.maxLoginTime }
    });

    dialogRef.afterClosed().pipe(takeUntil(this.destroy$)).subscribe(result => {
      if (result) {
        this.addRecord(checkInTime, 'Late', result.reason, result.notes);
      }
    });
  }

  private handleCheckInError(error: any) {
    console.error("Check-in failed:", error);
    this.notificationService.showNotification({
      type: 'error',
      message: 'Check-in failed: ' + (error instanceof Error ? error.message : 'Unknown error')
    });
    this.cdr.detectChanges();
  }

  private setupStatusPolling() {
    interval(30000).pipe( // Refresh every 30 seconds
      takeUntil(this.destroy$),
      filter(() => !this.isCheckedInToday())
    ).subscribe(() => {
      this.checkAttendanceStatus();
    });
  }

  private isCheckedInToday(): boolean {
    const today = formatDate(new Date(), 'yyyy-MM-dd', 'en-US');
    return this._attendanceRecords.some(r => r.date === today);
  }

  getUserLocation(): Promise<{ latitude: number; longitude: number }> {
    return new Promise((resolve, reject) => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.latitude = position.coords.latitude;
            this.longitude = position.coords.longitude;
            console.log("Location fetched:", this.latitude, this.longitude);
            resolve({ latitude: this.latitude, longitude: this.longitude });
          },
          (error) => {
            console.error("Error getting location:", error.message);
            this.notificationService.showNotification({
              type: 'error',
              message: 'Please allow location access to mark attendance.'
            });
            reject(error);
          }
        );
      } else {
        const message = 'Geolocation is not supported by this browser.';
        this.notificationService.showNotification({
          type: 'error',
          message: message
        });
        reject(new Error(message));
      }
    });
  }

  private addRecord(time: string, status: 'On Time' | 'Late', reason: string, notes: string) {
    const today = formatDate(new Date(), 'yyyy-MM-dd', 'en-US');
    const fullReason = notes ? `${reason} (${notes})` : reason;

    const attendancePayload: AttendanceRequest = {
      ldap: this.userInfo.ldap,
      reason: reason || undefined,
      latitude: this.latitude,
      longitude: this.longitude
    };

    this.attendanceService.markAttendance(attendancePayload).subscribe({
      next: (response: any) => {
        console.log('Mark attendance response:', response);

        if (response && response.id) {
          this.notificationService.showNotification({
            type: 'success',
            message: 'Attendance marked successfully'
          });

          const newRecord: AttendanceRecord = {
            id: response.id,
            date: formatDate(response.entryDate, 'yyyy-MM-dd', 'en-US'),
            checkIn: formatDate(response.entryTimestamp, 'HH:mm', 'en-US'),
            status: this.isLateCheckIn(response.entryTimestamp) ? 'Late' : 'On Time',
            reason: response.lateLoginReason || '-',
            isOutsideOffice: response.isOutsideOffice,
            isDefaulter: response.isDefaulter
          };

          console.log('New record created:', newRecord);

          // Update local records immediately
          this._attendanceRecords = [newRecord, ...this._attendanceRecords];
          this.dataSource.data = this._attendanceRecords;

          // Update column unique values for filtering
          this.collectUniqueColumnValues(this._attendanceRecords);

          // Update check-in state immediately
          this.checkedIn = true;
          this.checkedInTime = time;

          // Update check-in status to reflect the current state
          this.checkInStatus = {
            status: `Checked in at ${time}`,
            checkedIn: true,
            checkInTime: time,
            isLate: this.isLateCheckIn(response.entryTimestamp)
          };

          console.log('Updated checkInStatus:', this.checkInStatus);
          console.log('isUserCheckedInToday:', this.isUserCheckedInToday);

          // Force change detection
          this.cdr.detectChanges();

          // Also refresh from server to ensure consistency
          this.checkAttendanceStatus();
        } else {
          console.error('Invalid response from server:', response);
          throw new Error('Invalid response from server');
        }
      },
      error: (err) => {
        console.error('Attendance marking failed', err);
        this.notificationService.showNotification({
          type: 'error',
          message: 'Attendance Marking Failed,Please Try Later'
          // this.getErrorMessage(err)
        });
      }
    });
  }

  isRoleAllowed(): boolean {
    const allowedRoles = ['LEAD', 'MANAGER', 'ADMIN_OPS_MANAGER'];
    return this.userRole !== undefined && allowedRoles.includes(this.userRole);
  }

  canCheckIn(): boolean {
    return this.userRole === 'USER';
  }

  currentUser() {
    this.leaveService.managerDetails$.pipe(
      filter(data => !!data && data.length > 0),
      take(1)
    ).subscribe({
      next: (data) => {
        this.userInfo = data[0];
        console.log('User info:', this.userInfo);

        if (this.userInfo.ldap && this.userRole === 'USER') {
          this.checkAttendanceStatus();
        }
        if (this.userInfo.shift) {
          this.attendanceService.getShiftDetails(this.userInfo.shift).subscribe({
            next: (shiftDetails:any) => {
              this.shiftDetails = {
                code: shiftDetails.code,
                startTime: shiftDetails.startTime,
                endTime: shiftDetails.endTime,
                maxLoginTime: shiftDetails.maxLoginTime
              };
              console.log('Mapped shiftDetails:', this.shiftDetails);
              console.log('Fetched maxLoginTime:', this.shiftDetails.maxLoginTime);
            },
            error: (err) => {
              console.error('Failed to load shift details on init:', err);
            }
          });
        }

        this.loadAttendanceRecordsTemp();
      },
      error: (err) => {
        console.error('Error fetching user info:', err);
      }
    });
  }

  parseTimeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
  

  private updateCheckInState(status: CheckInStatusResponse) {
    this.checkInStatus = status;

    // Update local state to match the server response
    this.checkedIn = status.checkedIn;
    if (status.checkedIn && status.checkInTime) {
      this.checkedInTime = status.checkInTime;
    }

    this.cdr.detectChanges();
  }

  refreshAttendance(): void {
    this.currentUser(); // Will re-fetch user info + attendance
  }

  checkAttendanceStatus(ldap?: string) {
    // No longer need to pass LDAP - the backend uses authenticated user automatically
    this.attendanceService.getCheckInStatus().pipe(
      takeUntil(this.destroy$) // Add ngOnDestroy handler
    ).subscribe({
      next: (response) => {
        this.updateCheckInState(response);

        // Force reload if records don't match status
        const today = formatDate(new Date(), 'yyyy-MM-dd', 'en-US');
        if (response.checkedIn && !this._attendanceRecords.some(r => r.date === today)) {
          this.loadAttendanceRecordsTemp();
        }
      },
      error: (err) => {
        console.error('Status check failed:', err);
        this.updateCheckInState({
          status: 'Error checking status',
          checkedIn: false,
          checkInTime: '',
          isLate: false
        });
      }
    });
  }

  getLastWeekStartDate(): Date {
    const date = new Date();
    date.setDate(date.getDate() - 7);
    return date;
  }

  applyDateFilter(): void {
    console.log("Applying date Filter")
    this.loadAttendanceRecordsTemp();
  }

  downloadCSV(): void {
    const exportData = this.dataSource.data.map(item => ({
      'ID': item.id,
      'Date': item.date,
      'Check-In Time': item.checkIn,
      'Status': item.status,
      'Reason': item.reason,
      'Location': item.isOutsideOffice ? 'Outside' : 'Inside',
      'Defaulter': item.isDefaulter ? 'Yes' : 'No'
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Attendance Records');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'csv', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `attendance_records_${new Date().toISOString().split('T')[0]}.csv`);
  }

  getStatusClass(record: AttendanceRecord): string {
    if (record.isDefaulter) return 'non-compliant';
    return record.status === 'Late' ? 'late' : 'on-time';
  }

  // Getter method for template to check if user is checked in today
  get isUserCheckedInToday(): boolean {
    // Primary check: server status
    if (this.checkInStatus?.checkedIn) {
      return true;
    }

    // Secondary check: local records
    const today = new Date().toISOString().split('T')[0];
    const hasLocalRecord = this._attendanceRecords.some(record => record.date === today);

    console.log('isUserCheckedInToday - checkInStatus:', this.checkInStatus);
    console.log('isUserCheckedInToday - hasLocalRecord:', hasLocalRecord);
    console.log('isUserCheckedInToday - today:', today);
    console.log('isUserCheckedInToday - records:', this._attendanceRecords);

    return hasLocalRecord;
  }

  // Getter method for check-in status display
  get checkInStatusDisplay(): string {
    if (this.checkInStatus?.checkedIn && this.checkInStatus.status) {
      return this.checkInStatus.status;
    }

    // Fallback to local data if server status is not available
    const todayTime = this.getTodayCheckInTime();
    if (todayTime && todayTime !== '--:--') {
      return `Checked in at ${todayTime}`;
    }

    return '';
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}