/* src/app/attendance-management.component.css */

/* styles.css */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

.greeting {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    gap: 0.5rem;
}

.hello-static {
    font-weight: 500;
}

.name-animated {
    font-weight: 600;
    color: #3f51b5;
    /* Angular Material primary color */
}

@keyframes fadeName {

    0%,
    100% {
        opacity: 0;
        transform: translateY(10px);
    }

    20%,
    80% {
        opacity: 1;
        transform: translateY(0);
    }
}


.attendance-management {
    padding: 16px;
    font-family: Arial, sans-serif;
}

.attendance-management .mat-card-title {
    font-size: 1.2rem;
    font-weight: 500;
}


.today-attendance,
.recent-attendance {
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    background-color: #f5f5f5;
}

h1 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-align: center;
}

h2 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* Center the button in the card */

.mat-card {
    padding: 16px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mat-card-title {
    font-size: 1.2rem;
    font-weight: 500;
}

.mat-card-content {
    display: flex;
    flex-direction: column;
}

mat-card-content button {
    margin-top: 10px;
    /* Adjust as needed for spacing */
}

/* Modern UI Styles */
.requests-tab {
    padding: 24px;
    font-family: 'Segoe UI', Roboto, sans-serif;
    color: #333;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.tab-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* Button Styles */
.btn-primary,
.btn-secondary {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: white;
    color: #4b5563;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background-color: #f9fafb;
}

/* Filter Card */
.filter-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
}

.filter-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #374151;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.filter-group label {
    font-size: 14px;
    color: #4b5563;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.filter-btn {
    height: 100%;
    justify-content: center;
}

.filter-icon {
    width: 16px;
    height: 16px;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    margin-bottom: 20px;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table th {
    background-color: #f9fafb;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
    border-bottom: 1px solid #e5e7eb;
}

.modern-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    color: #4b5563;
    font-size: 14px;
}

.modern-table tr:last-child td {
    border-bottom: none;
}

.modern-table tr:hover {
    background-color: #f9fafb;
}

.text-right {
    text-align: right;
}

/* Status Badges */
/* Add this to your existing CSS */
.time-column {
    width: 90px;
    /* Fixed width for time column */
    white-space: nowrap;
}

/* Ensure table columns have consistent spacing */
.leave-history-table th,
.leave-history-table td {
    padding: 10px 12px;
}

/* Action Buttons */
.actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.text-btn {
    background: none;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 4px 0;
}

.approve-btn {
    color: #10b981;
}

.approve-btn:hover {
    color: #059669;
}

.deny-btn {
    color: #ef4444;
}

.deny-btn:hover {
    color: #dc2626;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-top: 1px solid #e5e7eb;
}

.pagination-info {
    font-size: 14px;
    color: #6b7280;
}

.pagination-info span {
    font-weight: 600;
    color: #374151;
}

.page-btn {
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-btn:hover {
    background: #f9fafb;
}

.page-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.header h2 {
    font-size: 24px;
    font-weight: 500;
    color: #3f51b5;
}

.current-date {
    color: #666;
    font-size: 14px;
}

.mat-table {
    width: 100%;
    margin-top: 20px;
}

.mat-header-cell {
    font-weight: 600;
    color: #333;
    background-color: #f5f5f5;
}

.mat-cell {
    vertical-align: middle;
    text-align: center;
}

/* Add this to your existing CSS */
.time-column {
    width: 90px;
    /* Fixed width for time column */
    white-space: nowrap;
}

/* Make status badges more compact */
.no-data {
    padding: 24px;
    text-align: center;
    color: #666;
}

.footer {
    margin-top: 16px;
    font-size: 14px;
    color: #666;
    text-align: right;
}

.tab-content {
    padding: 20px 0;
}

/* Container styles */
.leave-history-container {
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.leave-history-container h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 20px;
    font-weight: 600;
}

/* Table styles */
.leave-history-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.leave-history-table th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #555;
    border-bottom: 2px solid #ddd;
}

.leave-history-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    color: #333;
}

.leave-history-table tr:last-child td {
    border-bottom: none;
}

/* Status badges */

.status-approved {
    background-color: #e6f7ee;
    color: #00a854;
}

.status-pending {
    background-color: #fff7e6;
    color: #fa8c16;
}

.status-rejected {
    background-color: #fff1f0;
    color: #f5222d;
}

/* Pagination styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.showing-text {
    font-size: 14px;
    color: #666;
}

.pagination-controls {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination-controls button,
.pagination-controls span {
    padding: 5px 10px;
    cursor: pointer;
    border: 1px solid #ddd;
    background: white;
    border-radius: 3px;
    min-width: 30px;
    text-align: center;
    font-size: 14px;
}

.pagination-controls button:hover:not(:disabled),
.pagination-controls span:hover:not(.active) {
    background-color: #f0f0f0;
}

.pagination-controls span.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Main container */
.attendance-container {
    padding: 24px;
    font-family: 'Roboto', sans-serif;
    max-width: 1200px;
    margin: 0 auto;
}


/* Today's attendance section */
.today-attendance {
    margin-bottom: 30px;
}

.today-attendance h2 {
    font-size: 24px;
    margin-bottom: 8px;
    color: #3f51b5;
}


.checkin-card {
    max-width: 500px;
    margin: 0 auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.instruction {
    font-size: 16px;
    margin-bottom: 20px;
    color: #555;
}

.checkin-btn,
.checkout-btn {
    width: 150px;
}

.checked-in-status {
    display: flex;
    align-items: center;
    gap: 20px;
}

.checked-in-time {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin: 0;
}

/* Actions toolbar */
.actions-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 4px;
}

.action-group {
    display: flex;
    align-items: center;
}

.date-range-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-range-field {
    width: 280px;
}

.filter-toggle {
    margin-right: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .actions-toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .date-range-group {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .filter-grid {
        grid-template-columns: 1fr;
    }

    .header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 10px;
    }
}

/* Add these styles to your existing CSS file */

/* Status Badges - Enhanced */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    min-width: 80px;
    text-align: center;
}

.status-badge.on-time {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.status-badge.late {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ef9a9a;
}

.status-badge.approved {
    background-color: #e6f7ee;
    color: #00a854;
}

.status-badge.pending {
    background-color: #fff7e6;
    color: #fa8c16;
}

.status-badge.rejected {
    background-color: #fff1f0;
    color: #f5222d;
}

/* Defaulter Badges */
.compliance-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

.compliance-badge.compliant {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.compliance-badge.non-compliant {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ef9a9a;
}

/* Location Badges - Enhanced */
.location-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.location-badge.inside {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.location-badge.outside {
    background-color: #fff3e0;
    color: #e65100;
    border: 1px solid #ffe0b2;
}

/* Table cell alignment */
.mat-cell {
    vertical-align: middle;
}

/* Hover effects for better interactivity */
.status-badge.late:hover,
.compliance-badge.non-compliant:hover {
    background-color: #ffcdd2;
    transition: background-color 0.2s ease;
}

.status-badge.on-time:hover,
.compliance-badge.compliant:hover {
    background-color: #c8e6c9;
    transition: background-color 0.2s ease;
}

/* Icon colors for location */
.location-badge .mat-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
}

.location-badge.inside .mat-icon {
    color: #2e7d32;
}

.location-badge.outside .mat-icon {
    color: #e65100;
}

/* Adding new Styles and Replacing Old one  */

body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

/* Add these styles to your existing CSS */
.checked-in-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
}
/* Disabled check-in button style */
button[disabled] {
    background-color: #e0e0e0 !important;
    color: #9e9e9e !important;
    cursor: not-allowed;
}

.check-in-status {
    display: flex;
    gap: 0.5rem;
    /* Adds space between text and time */
    align-items: center;
}

.checked-in-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    /* Green color for checked-in */
    font-weight: 500;
}

.success-icon {
    color: #4CAF50;
}